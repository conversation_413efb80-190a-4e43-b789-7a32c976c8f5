.card-container{
	display: flex;
	gap: 2rem;
	justify-content: center;
	flex-direction: row;
	/* align-items: center; */
	margin: 60px 0px 60px 0px;
	flex-wrap: wrap;
	padding: 1.2rem;
}
.card{
	width: 100%;
    max-width: 280px; 
    background: #101130; 
    color: white;
    border-radius: 15px;
    padding: 20px;
    text-align: center;
    box-shadow: 4px 6px 20px rgba(0, 0, 0, 0.4);
    position: relative;
    border-bottom: 3px solid red;
    transition: all 0.4s ease-in-out;
}
.profile img {
    width: 90px;
    border-radius: 50%;
    border: 4px solid red;
    transition: border 0.4s ease-in-out;
}
.delgateName {
    margin: 10px 0 5px;
    font-size: 18px;
    font-weight: 600;
    transition: color 0.4s ease-in-out;
}
.designation {
	color: #ddd;
    font-size: 14px;
    margin-bottom: 8px;
    transition: color 0.4s ease-in-out;
	font-weight: bold;
}
.orgName{
	color: #ddd;
    font-size: 13px;
    margin-bottom: 8px;
    transition: color 0.4s ease-in-out;
}
.orgLine {
    width: 25%;
    border: 1px solid red;
    margin: 5px auto;
    transition: border 0.4s ease-in-out;
	text-align: center;
	align-self: center;
}
.card:hover {
    background: white;
    color: black;
    box-shadow: 4px 6px 20px rgba(0, 0, 0, 0.2);
    border-bottom: 3px solid #197DB8; /* Light Mode Border Color */
}
.card:hover .profile img {
    border: 4px solid #197DB8; /* Border Changes on Hover */
}
.card:hover p,
.card:hover h2,
.card:hover .orgName {
    color: black;
}
.card:hover .orgLine {
    border: 1px solid #197DB8;
}

/* Animation for Load More */
.fadeInUp {
    opacity: 0;
    transform: translateY(20px);
    animation: fadeInUp 0.5s forwards;
}

@keyframes fadeInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}