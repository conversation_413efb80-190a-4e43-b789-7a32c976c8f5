/* No Photo Card Styles */
.no-photo-card {
    background-color: #f5f5f5;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
    text-align: center;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    min-height: 150px;
}

.no-photo-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.no-photo-card .delgateName {
    font-size: 18px;
    font-weight: 600;
    color: #4965AD;
    margin-bottom: 5px;
}

.no-photo-card .designation {
    font-size: 14px;
    color: #666;
    margin-bottom: 0;
}

/* Card container for no-photo cards */
.no-photo-card-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 20px;
    margin-top: 30px;
}

@media (max-width: 767px) {
    .no-photo-card-container {
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    }
}
