/* CSS for Introduction Section Only */
.intro-section-container {
    position: relative;
    padding: 100px 0 80px;
    background-color: #f9f9fa;
    overflow: hidden;
    border-bottom: 1px solid #eee;
    box-shadow: inset 0 0 30px rgba(0,0,0,0.03);
}

.intro-bg-element {
    position: absolute;
    width: 600px;
    height: 600px;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-image: url('../images/dummy/places/preview.png');
    background-size: contain;
    background-position: center;
    background-repeat: no-repeat;
    opacity: 0.55;
    z-index: 0;
    animation: rotateBackground 60s linear infinite;
    transform-origin: center;
}

@keyframes rotateBackground {
    0% {
        transform: translate(-50%, -50%) rotate(0deg);
    }
    100% {
        transform: translate(-50%, -50%) rotate(360deg);
    }
}

.intro-text-container {
    position: relative;
    z-index: 1;
    text-align: center;
    max-width: 800px;
    margin: 0 auto;
    padding: 0 20px;
}

.intro-heading {
    font-size: 42px;
    margin-bottom: 25px;
    color: #57428D;
    font-weight: 700;
    position: relative;
    display: inline-block;
    padding-bottom: 15px;
    text-shadow: 0 1px 2px rgba(0,0,0,0.05);
}

.intro-heading:after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 80px;
    height: 3px;
    background: linear-gradient(to right, #4965AD, #57428D);
    transform: translateX(-50%);
    border-radius: 2px;
}

.intro-paragraph {
    font-size: 18px;
    line-height: 1.7;
    color: #444;
    margin-bottom: 0;
}

/* Responsive Styles */
@media only screen and (max-width: 1200px) {
    .intro-bg-element {
        width: 500px;
        height: 500px;
    }
}

@media only screen and (max-width: 992px) {
    .intro-section-container {
        padding: 80px 0 60px;
    }
    
    .intro-bg-element {
        width: 400px;
        height: 400px;
        opacity: 0.4;
    }
    
    .intro-heading {
        font-size: 36px;
        margin-bottom: 20px;
    }
    
    .intro-paragraph {
        font-size: 16px;
    }
}

@media only screen and (max-width: 768px) {
    .intro-section-container {
        padding: 60px 0 40px;
    }
    
    .intro-bg-element {
        width: 350px;
        height: 350px;
    }
    
    .intro-heading {
        font-size: 32px;
    }
}

@media only screen and (max-width: 576px) {
    .intro-section-container {
        padding: 50px 0 30px;
    }
    
    .intro-bg-element {
        width: 280px;
        height: 280px;
        opacity: 0.35;
    }
    
    .intro-heading {
        font-size: 28px;
        padding-bottom: 10px;
        margin-bottom: 15px;
    }
    
    .intro-heading:after {
        width: 60px;
        height: 2px;
    }
    
    .intro-paragraph {
        font-size: 15px;
        line-height: 1.6;
    }
    
    .intro-text-container {
        padding: 0 15px;
    }
}

@media only screen and (max-width: 375px) {
    .intro-bg-element {
        width: 240px;
        height: 240px;
    }
    
    .intro-heading {
        font-size: 24px;
    }
    
    .intro-paragraph {
        font-size: 14px;
    }
} 