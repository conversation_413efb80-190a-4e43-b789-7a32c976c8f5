RewriteEngine On

# First, redirect index to root URL - this needs to come before other rules
RewriteRule ^index$ / [R=301,L]
RewriteRule ^index\.html$ / [R=301,L]

# Then handle any request with .html extension
RewriteCond %{THE_REQUEST} \s/([^.]+)\.html [NC]
RewriteRule ^ /%1 [R=301,L]

# For clean URLs, internally map to .html files
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME}.html -f
RewriteRule ^([^/]+)/?$ $1.html [L]

# Ensure index.html loads on root request
DirectoryIndex index.html
