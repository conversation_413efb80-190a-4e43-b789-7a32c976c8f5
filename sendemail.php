<?php

// Define some constants
define( "RECIPIENT_NAME", "<PERSON> Doe" );
define( "RECIPIENT_EMAIL", "<EMAIL>" );


// Read the form values
$success = false;
$senderName = isset( $_POST['username'] ) ? preg_replace( "/[^\s\S\.\-\_\@a-zA-Z0-9]/", "", $_POST['username'] ) : "";
$senderPhone = isset( $_POST['phone'] ) ? preg_replace( "/[^\s\S\.\-\_\@a-zA-Z0-9]/", "", $_POST['phone'] ) : "";
$senderEmail = isset( $_POST['email'] ) ? preg_replace( "/[^\.\-\_\@a-zA-Z0-9]/", "", $_POST['email'] ) : "";
$subject = isset( $_POST['subject'] ) ? preg_replace( "/[^\s\S\.\-\_\@a-zA-Z0-9]/", "", $_POST['subject'] ) : "";
$message = isset( $_POST['message'] ) ? preg_replace( "/(From:|To:|BCC:|CC:|Subject:|Content-Type:)/", "", $_POST['message'] ) : "";

// If all values exist, send the email
if ( $senderName && $senderEmail && $senderPhone && $subject && $message) {
  $recipient = RECIPIENT_NAME . " <" . RECIPIENT_EMAIL . ">";
  $headers = "From: " . $senderName . "";
  $msgBody = " Email: ". $senderEmail .  " Phone: ". $senderPhone . " Subject: ". $subject .  " Message: " . $message . "";
  $success = mail( $recipient, $headers, $msgBody );

  echo "<script>alert('Your message has been sucessfully submitted Thanks. 🙂');</script>";
  echo "<script>document.location.href='index.html'</script>";
}

else{
  echo "<script>alert('Mail was not Send');</script>";
  echo "<script>document.location.href='contact.html'</script>";
}

?>