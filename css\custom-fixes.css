/* Custom fixes for ISAR 2026 website to match with live site */

/* Remove any unwanted spacing in header */
body {
    margin: 0 !important;
    padding: 0 !important;
}

/* Fix header spacing - no extra space */
.header-span {
    height: 0 !important;
    display: none !important;
}

.header-span.style-two {
    height: 0 !important;
    display: none !important;
}

/* Fix main header positioning */
.main-header {
    position: relative !important;
    left: 0 !important;
    top: 0 !important;
    width: 100% !important;
    z-index: 999 !important;
    background-color: transparent !important;
}

/* Header top styling - match with live site */
.main-header .header-top {
    background-color: #6839A3 !important;
    padding: 0 !important;
    width: 100% !important;
    display: block !important;
    clear: both !important;
    overflow: hidden !important;
}

/* Fix top header display and alignment */
.main-header .header-top .top-left {
    float: left !important;
    display: block !important;
    position: relative !important;
    width: auto !important;
    padding: 0 !important;
    margin: 0 !important;
    text-align: left !important;
}

.main-header .header-top .top-right,
.main-header .header-top .pull-right {
    float: right !important;
    display: block !important;
    position: relative !important;
    width: auto !important;
    padding: 0 !important;
    margin: 0 !important;
    text-align: right !important;
}

/* Force float left/right with !important */
.pull-left {
    float: left !important;
}

.pull-right {
    float: right !important;
}

/* Fix header top clearfix */
.main-header .header-top .clearfix:after {
    content: "" !important;
    display: table !important;
    clear: both !important;
}

/* Fix contact info display */
.header-contact-info {
    position: relative !important;
    display: flex !important;
    flex-direction: row !important;
    align-items: center !important;
    padding: 0 !important;
    margin: 0 !important;
}

.header-contact-info .call,
.header-contact-info .email {
    position: relative !important;
    padding: 10px 0 !important;
    padding-left: 30px !important;
    margin-right: 30px !important;
    font-size: 14px !important;
    line-height: 30px !important;
    color: #ffffff !important;
    font-weight: 400 !important;
    display: inline-block !important;
}

.header-contact-info .call a,
.header-contact-info .email a {
    color: #ffffff !important;
    font-size: 14px !important;
    text-decoration: none !important;
}

.header-contact-info .call img,
.header-contact-info .email img {
    position: absolute !important;
    left: 0 !important;
    top: 50% !important;
    margin-top: -10px !important;
    width: 20px !important;
    height: 20px !important;
}

/* Social links */
.main-header .header-top .social-links {
    position: relative !important;
    display: inline-block !important;
    margin: 0 !important;
    margin-left: 20px !important;
    padding: 0 !important;
}

.main-header .header-top .social-links li {
    position: relative !important;
    float: left !important;
    margin-right: 18px !important;
    font-size: 14px !important;
    line-height: 30px !important;
    padding: 10px 0 !important;
    list-style-type: none !important;
}

.main-header .header-top .social-links li a {
    position: relative !important;
    color: #ffffff !important;
    font-size: 14px !important;
    text-decoration: none !important;
}

/* Registration button */
.main-header .header-top .register-btn {
    position: relative !important;
    display: inline-block !important;
    padding: 10px 20px !important;
    margin-left: 20px !important;
    background-color: #f20487 !important;
    border-radius: 5px !important;
    color: #ffffff !important;
    font-size: 14px !important;
    line-height: 20px !important;
    text-decoration: none !important;
}

/* Fix logo sizes to match with live site */
.main-header .btn-logo {
    width: 85px !important;
    max-width: 85px !important;
    margin-left: 25px !important;
    padding: 4.5px 0px !important;
    display: inline-block !important;
    vertical-align: middle !important;
}

.main-header .btn-logo img {
    max-height: 45px !important;
    width: auto !important;
    height: auto !important;
}

.logo-box.logo-header {
    float: left !important;
}

.main-header .logo-box.logo-header .logo {
    padding: 0 !important;
}

.main-header .logo-box.logo-header .logo img {
    max-height: 60px !important;
    width: auto !important;
}

/* Fix navigation display - exactly like live site */
.main-menu .navigation {
    position: relative !important;
    display: flex !important;
    align-items: center !important;
    margin: 0 !important;
    padding: 0 !important;
}

.main-menu .navigation > li {
    position: relative !important;
    float: left !important;
    display: block !important;
    padding: 20px 0 !important;
    margin-right: 30px !important;
    list-style-type: none !important;
}

.main-menu .navigation > li > a {
    position: relative !important;
    display: block !important; 
    text-align: center !important;
    opacity: 1 !important;
    font-size: 15px !important;
    line-height: 30px !important;
    font-weight: 500 !important;
    color: #3f4161 !important;
    padding: 0px !important;
    text-transform: capitalize !important;
    text-decoration: none !important;
}

/* Fix banner/slider to remove extra space */
.banner-section {
    position: relative !important;
    padding: 0 !important;
    margin: 0 !important;
}

/* Fix headings */
.sec-title h2 {
    position: relative !important;
    display: block !important;
    font-size: 40px !important;
    line-height: 1.2em !important;
    color: #1e1f36 !important;
    font-weight: 700 !important;
    text-transform: capitalize !important;
    margin-bottom: 18px !important;
}

.sec-title .title {
    position: relative !important;
    display: block !important;
    font-size: 16px !important;
    line-height: 24px !important;
    color: #ff9fd4 !important;
    font-weight: 500 !important;
    margin-bottom: 15px !important;
}

.sec-title .text {
    position: relative !important;
    font-size: 16px !important;
    line-height: 26px !important;
    color: #888888 !important;
    margin-top: 30px !important;
    margin-bottom: 0 !important;
}

/* Fix Event venue section */
.event-info-section {
    position: relative !important;
    padding: 100px 0 50px !important;
    background: rgb(105,57,172) !important;
    background: linear-gradient(90deg, rgba(105,57,172,1) 0%, rgba(88,86,214,1) 100%) !important;
    z-index: 1 !important;
}

.event-info-section .sec-title.style-two .title,
.event-info-section .sec-title.style-two h2 {
    color: #ffffff !important;
}

.event-info-section h4 {
    color: #ffffff !important;
}

.event-info-section .text {
    color: rgba(255, 255, 255, 0.8) !important;
}

.event-info-section .info-list li a,
.event-info-section .info-list li {
    color: rgba(255, 255, 255, 0.8) !important;
}

/* Fix Committee section */
.committee-section {
    padding: 50px 0 !important;
}

.card-container {
    display: flex !important;
    flex-wrap: wrap !important;
    justify-content: center !important;
    gap: 20px !important;
}

.committee-section .card {
    margin: 10px !important;
    padding: 20px !important;
    text-align: center !important;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1) !important;
    border-radius: 10px !important;
    background-color: #fff !important;
    width: 200px !important;
}

/* Fix footer styling */
.main-footer {
    position: relative !important;
    background-color: #1e1f36 !important;
    padding: 0 !important;
    margin: 0 !important;
}

.footer-bottom {
    background-color: #191a30 !important;
    padding: 0 !important;
    margin: 0 !important;
}

.footer-bottom .inner-container {
    position: relative !important;
    border-top: 1px solid #222222 !important;
    padding: 0 !important;
    margin: 0 !important;
}

.footer-bottom .powered-by-text,
.footer-bottom .copyright-text {
    position: relative !important;
    color: #999999 !important;
    font-size: 14px !important;
    line-height: 20px !important;
    padding: 20px 0 !important;
    margin: 0 !important;
}

/* Hide Color Switcher */
.color-palate, 
#search-popup {
    display: none !important;
}

/* Responsive fixes */
@media only screen and (max-width: 767px) {
    .main-header .header-top .top-left,
    .main-header .header-top .pull-right {
        width: 100% !important;
        float: none !important;
        text-align: center !important;
    }
    
    .header-contact-info {
        justify-content: center !important;
        flex-wrap: wrap !important;
    }
    
    .header-contact-info .call,
    .header-contact-info .email {
        margin-right: 0 !important;
        padding: 5px 0 5px 25px !important;
        width: 100% !important;
        text-align: center !important;
    }
    
    .main-header .header-top .social-links {
        margin-left: 0 !important;
    }
    
    .main-header .header-top .social-links li {
        margin-right: 10px !important;
    }
    
    .main-header .header-top .register-btn {
        margin-left: 0 !important;
        padding: 8px 15px !important;
    }
    
    .main-header .btn-logo {
        margin-left: 10px !important;
    }
}

/* Force white icons in header */
.header-contact-info img {
    filter: invert(1) !important;
} 