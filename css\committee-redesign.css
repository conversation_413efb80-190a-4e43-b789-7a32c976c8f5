/* Committee Section Redesign - Premium Professional */
.committee-section-redesign {
    padding: 60px 0;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    position: relative;
    overflow: hidden;
}

.committee-section-redesign::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
        radial-gradient(circle at 20% 20%, rgba(102, 126, 234, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 80%, rgba(255, 107, 107, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(86, 171, 47, 0.05) 0%, transparent 50%);
    z-index: 1;
}

.committee-section-redesign .auto-container {
    position: relative;
    z-index: 2;
}

.committee-section-redesign .auto-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
}

/* Section Titles - Enhanced Professional Design */
.committee-main-title {
    text-align: center;
    font-size: 48px;
    font-weight: 800;
    color: #1a202c;
    margin-bottom: 70px;
    position: relative;
    letter-spacing: -1px;
    text-transform: uppercase;
}



.committee-main-title::after {
    content: '';
    position: absolute;
    bottom: -20px;
    left: 50%;
    transform: translateX(-50%);
    width: 120px;
    height: 3px;
    background: linear-gradient(90deg, #ff6b6b, #667eea, #56ab2f);
    border-radius: 1.5px;
    box-shadow: 0 2px 10px rgba(102, 126, 234, 0.3);
}

.committee-subsection-title {
    text-align: center;
    font-size: 32px;
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 45px;
    position: relative;
    letter-spacing: -0.5px;
    padding: 0 20px;
}

.committee-subsection-title::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    width: 40px;
    height: 2px;
    background: linear-gradient(90deg, transparent, #667eea);
    transform: translateY(-50%);
}

.committee-subsection-title::after {
    content: '';
    position: absolute;
    top: 50%;
    right: 0;
    width: 40px;
    height: 2px;
    background: linear-gradient(90deg, #667eea, transparent);
    transform: translateY(-50%);
}

/* Committee Grid Layout */
.committee-grid {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
    gap: 40px;
    margin-bottom: 80px;
}

.committee-grid.national-executive {
    margin-bottom: 60px;
}

.committee-grid.organizing-team {
    margin-bottom: 40px;
}

/* Member Card */
.member-card {
    text-align: center;
    background: transparent;
    padding: 20px 10px;
    transition: all 0.3s ease;
    position: relative;
    min-width: 180px;
    max-width: 180px;
}

.member-card:hover {
    transform: translateY(-5px);
}

/* Profile Image Container - Hexagonal Shape */
.member-profile {
    position: relative;
    width: 140px;
    height: 140px;
    margin: 0 auto 20px;
    background: transparent;
    transition: all 0.4s ease;
    overflow: visible;
}

.member-profile::before {
    content: '';
    position: absolute;
    top: -3px;
    left: -3px;
    right: -3px;
    bottom: -3px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    clip-path: polygon(30% 0%, 70% 0%, 100% 50%, 70% 100%, 30% 100%, 0% 50%);
    z-index: 0;
    border-radius: 0;
}

@keyframes borderPulse {
    0%, 100% {
        opacity: 1;
        transform: scale(1);
    }
    50% {
        opacity: 0.8;
        transform: scale(1.05);
    }
}



@keyframes outerGlow {
    0% {
        transform: scale(1);
        opacity: 0.3;
    }
    100% {
        transform: scale(1.1);
        opacity: 0.1;
    }
}

.member-profile img {
    width: 140px;
    height: 140px;
    object-fit: cover;
    object-position: center center;
    position: relative;
    z-index: 1;
    clip-path: polygon(30% 0%, 70% 0%, 100% 50%, 70% 100%, 30% 100%, 0% 50%);
    transition: all 0.3s ease;
    display: block;
}

.member-profile:hover img {
    transform: scale(1.15);
}



/* Member Details - Enhanced Typography */
.member-name {
    font-size: 18px;
    font-weight: 700;
    color: #1a202c;
    margin-bottom: 8px;
    line-height: 1.3;
    letter-spacing: -0.3px;
    transition: all 0.3s ease;
    text-align: center;
}

.member-designation {
    font-size: 14px;
    color: #667eea;
    font-weight: 600;
    line-height: 1.4;
    margin-bottom: 0;
    transition: all 0.3s ease;
    text-align: center;
    margin-top: 5px;
}

/* Special styling for specific positions */
.member-card.president .member-profile::before {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
    box-shadow: 0 4px 20px rgba(255, 107, 107, 0.5);
}

.member-card.president .member-profile::after {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 50%, #ff9ff3 100%);
}

.member-card.president .member-profile {
    filter: drop-shadow(0 8px 16px rgba(0, 0, 0, 0.15)) drop-shadow(0 0 20px rgba(255, 107, 107, 0.4));
}

.member-card.secretary .member-profile::before {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    box-shadow: 0 4px 20px rgba(102, 126, 234, 0.5);
}

.member-card.secretary .member-profile::after {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
}

.member-card.secretary .member-profile {
    filter: drop-shadow(0 8px 16px rgba(0, 0, 0, 0.15)) drop-shadow(0 0 20px rgba(102, 126, 234, 0.4));
}

.member-card.organizing .member-profile::before {
    background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
    box-shadow: 0 4px 20px rgba(86, 171, 47, 0.5);
}

.member-card.organizing .member-profile::after {
    background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 50%, #88d8c0 100%);
}

.member-card.organizing .member-profile {
    filter: drop-shadow(0 8px 16px rgba(0, 0, 0, 0.15)) drop-shadow(0 0 20px rgba(86, 171, 47, 0.4));
}

/* Responsive Design */
@media (max-width: 1200px) {
    .committee-grid {
        gap: 30px;
    }
    
    .member-card {
        min-width: 160px;
        max-width: 160px;
    }
    
    .member-profile {
        width: 120px;
        height: 120px;
    }
}

@media (max-width: 992px) {
    .committee-grid {
        gap: 25px;
    }
    
    .member-card {
        min-width: 150px;
        max-width: 150px;
        padding: 15px 8px;
    }
    
    .member-profile {
        width: 110px;
        height: 110px;
    }
    
    .member-name {
        font-size: 15px;
    }
    
    .member-designation {
        font-size: 12px;
    }
}

@media (max-width: 768px) {
    .committee-section-redesign {
        padding: 80px 0;
    }
    
    .committee-main-title {
        font-size: 36px;
        margin-bottom: 50px;
    }
    
    .committee-main-title::after {
        width: 80px;
    }
    
    .committee-subsection-title {
        font-size: 26px;
        margin-bottom: 35px;
        padding: 0 15px;
    }
    
    .committee-subsection-title::before,
    .committee-subsection-title::after {
        width: 30px;
    }
    
    .committee-grid {
        gap: 25px;
        margin-bottom: 60px;
    }
    
    .member-card {
        min-width: 160px;
        max-width: 160px;
        padding: 15px 8px;
    }
    
    .member-profile {
        width: 120px;
        height: 120px;
        margin-bottom: 15px;
    }
    
    .member-profile img {
        width: 120px;
        height: 120px;
    }
    
    .member-name {
        font-size: 16px;
    }
    
    .member-designation {
        font-size: 12px;
        padding: 4px 8px;
    }
}

@media (max-width: 576px) {
    .committee-grid {
        justify-content: center;
        gap: 15px;
    }
    
    .member-card {
        min-width: 120px;
        max-width: 120px;
        padding: 12px 6px;
    }
    
    .member-profile {
        width: 90px;
        height: 90px;
        margin-bottom: 12px;
    }
    
    .member-name {
        font-size: 13px;
        margin-bottom: 5px;
    }
    
    .member-designation {
        font-size: 10px;
    }
}

/* Enhanced Hover Effects */
.member-card:hover .member-name {
    color: #667eea;
    transform: translateY(-2px);
}

.member-card:hover .member-designation {
    transform: translateY(-1px);
    color: #4c51bf;
}

.member-card:hover .member-profile {
    transform: translateY(-8px) scale(1.05);
}

.member-card:hover .member-profile::before {
    opacity: 1;
    transform: scale(1.1);
}

.member-card:hover .member-profile::before {
    animation: borderPulse 1.5s ease-in-out infinite, borderRotate 8s linear infinite;
    box-shadow: 0 6px 30px rgba(102, 126, 234, 0.6);
}

@keyframes borderRotate {
    0% {
        filter: hue-rotate(0deg);
    }
    100% {
        filter: hue-rotate(360deg);
    }
}

/* Animation for cards */
.member-card {
    opacity: 0;
    transform: translateY(30px);
    animation: fadeInUp 0.6s ease forwards;
}

.member-card:nth-child(1) { animation-delay: 0.1s; }
.member-card:nth-child(2) { animation-delay: 0.2s; }
.member-card:nth-child(3) { animation-delay: 0.3s; }
.member-card:nth-child(4) { animation-delay: 0.4s; }
.member-card:nth-child(5) { animation-delay: 0.5s; }

@keyframes fadeInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
} 