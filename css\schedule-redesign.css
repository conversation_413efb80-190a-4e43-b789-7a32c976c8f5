/* Modern Event Schedule - ISAR Conference 2026 
   Custom designed for Indian events with cultural aesthetics */

/* Schedule Section Base Styles */
.schedule-section {
    padding: 40px 0;
    position: relative;
    background: linear-gradient(135deg, #f8f8f8 0%, #ffffff 100%);
    overflow: hidden;
}

/* Indian Pattern Background */
.schedule-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='80' height='80' viewBox='0 0 80 80'%3E%3Cg fill-opacity='0.03'%3E%3Cpath fill='%23FBD4D2' d='M20 20h20v20H20V20zm40 0h20v20H60V20zm0 40h20v20H60V60zM20 60h20v20H20V60z'/%3E%3Cpath fill='%2357428D' d='M0 0h20v20H0V0zm40 0h20v20H40V0zm40 40h20v20H80V40zm0-40h20v20H80V0zM0 40h20v20H0V40zm0 40h20v20H0V60z'/%3E%3C/g%3E%3C/svg%3E");
    opacity: 0.05;
    z-index: 1;
}

.schedule-section .auto-container {
    position: relative;
    z-index: 3;
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
}

/* Section Title Styling */
.schedule-section .section-title {
    position: relative;
    margin-bottom: 60px;
    text-align: center;
}

.schedule-section .section-title .title-text {
    font-size: 16px;
    display: inline-block;
    padding: 10px 30px;
    background: linear-gradient(45deg, #4965AD 0%, #57428D 100%);
    color: #fff;
    border-radius: 30px;
    font-weight: 600;
    box-shadow: 0 5px 15px rgba(87, 66, 141, 0.2);
    text-transform: uppercase;
    letter-spacing: 1px;
    margin-bottom: 15px;
}

.schedule-section .section-title h2 {
    font-size: 42px;
    font-weight: 800;
    margin-bottom: 15px;
    position: relative;
    color: #212121;
    letter-spacing: -0.5px;
}

.schedule-section .section-title h2 span {
    position: relative;
    z-index: 1;
}

.schedule-section .section-title h2 span::after {
    content: '';
    position: absolute;
    width: 100%;
    height: 10px;
    bottom: 5px;
    left: 0;
    background: linear-gradient(90deg, rgba(73, 101, 173, 0.2), rgba(87, 66, 141, 0.2));
    z-index: -1;
    border-radius: 5px;
}

.schedule-section .section-title .venue-text {
    font-size: 18px;
    color: #424242;
    margin-top: 15px;
    font-weight: 500;
}

/* Schedule Tabs */
.schedule-tabs {
    position: relative;
    margin-bottom: 50px;
    text-align: center;
}

.schedule-tabs .tab-buttons {
    position: relative;
    display: inline-flex;
    justify-content: center;
    gap: 15px;
    flex-wrap: wrap;
    background: #fff;
    padding: 10px;
    border-radius: 50px;
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.06);
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.schedule-tabs .tab-btn {
    position: relative;
    padding: 12px 25px;
    font-size: 16px;
    line-height: 1.5;
    font-weight: 700;
    color: #424242;
    text-transform: uppercase;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.schedule-tabs .tab-btn span {
    position: relative;
    display: inline-block;
}

.schedule-tabs .tab-btn.active-btn {
    background: linear-gradient(45deg, #57428D 0%, #4965AD 100%);
    color: #fff;
    box-shadow: 0 5px 15px rgba(87, 66, 141, 0.3);
}

/* Schedule Content */
.schedule-content {
    position: relative;
}

.schedule-block {
    position: relative;
    display: none;
    animation: fadeIn 0.6s ease forwards;
}

.schedule-block.active-tab {
    display: block;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Schedule Timeline */
.schedule-timeline {
    position: relative;
    padding: 0;
    margin: 0;
    list-style: none;
}

.schedule-timeline::before {
    content: '';
    position: absolute;
    left: 30px;
    top: 10px;
    bottom: 10px;
    width: 3px;
    background: linear-gradient(to bottom, #4965AD 0%, #57428D 50%, #FBD4D2 100%);
    border-radius: 2px;
}

.timeline-item {
    position: relative;
    padding-left: 80px;
    margin-bottom: 30px;
    padding-bottom: 30px;
}

.timeline-item:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
}

/* Timeline Dot */
.timeline-dot {
    position: absolute;
    left: 20px;
    top: 20px;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: #fff;
    border: 5px solid #4965AD;
    box-shadow: 0 0 0 4px rgba(73, 101, 173, 0.1);
    z-index: 2;
}

.timeline-dot::before {
    content: '';
    position: absolute;
    left: -15px;
    top: 50%;
    margin-top: -1px;
    width: 15px;
    height: 2px;
    background: #4965AD;
}

/* Timeline Content */
.timeline-content {
    position: relative;
    background: #fff;
    border-radius: 15px;
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.06);
    padding: 25px 30px;
    transition: all 0.3s ease;
    border-left: 5px solid #4965AD;
    width: 100%;
    box-sizing: border-box;
}

.timeline-content:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

/* Event Time */
.event-time {
    display: inline-flex;
    align-items: center;
    background: linear-gradient(45deg, rgba(73, 101, 173, 0.1), rgba(87, 66, 141, 0.1));
    border-radius: 20px;
    padding: 5px 15px;
    margin-bottom: 15px;
    border: 1px solid rgba(73, 101, 173, 0.2);
}

.event-time .icon {
    margin-right: 8px;
    color: #4965AD;
    font-size: 16px;
}

.event-time span {
    font-size: 14px;
    font-weight: 600;
    color: #616161;
}

/* Event Title */
.event-title {
    font-size: 20px;
    font-weight: 700;
    color: #212121;
    margin-bottom: 8px;
    line-height: 1.4;
}

.event-title.featured {
    font-size: 22px;
    color: #57428D;
}

/* Event Description */
.event-description {
    font-size: 15px;
    color: #616161;
    line-height: 1.6;
}

/* Special Event Styling */
.special-event .timeline-content {
    border-left: 5px solid #57428D;
    background: linear-gradient(45deg, rgba(87, 66, 141, 0.05), rgba(255, 255, 255, 1));
}

.special-event .timeline-dot {
    border-color: #57428D;
    box-shadow: 0 0 0 4px rgba(87, 66, 141, 0.1);
}

.special-event .timeline-dot::before {
    background: #57428D;
}

.special-event .event-title {
    color: #57428D;
}

/* Workshop Styling */
.workshop-item .timeline-content {
    border-left: 5px solid #FBD4D2;
    background: linear-gradient(45deg, rgba(251, 212, 210, 0.05), rgba(255, 255, 255, 1));
}

.workshop-item .timeline-dot {
    border-color: #FBD4D2;
    box-shadow: 0 0 0 4px rgba(251, 212, 210, 0.1);
}

.workshop-item .timeline-dot::before {
    background: #FBD4D2;
}

/* Workshop Category */
.workshop-category {
    display: inline-block;
    font-size: 14px;
    font-weight: 600;
    color: #57428D;
    margin-bottom: 10px;
    text-transform: uppercase;
    letter-spacing: 1px;
    position: relative;
}

.workshop-category::after {
    content: '';
    position: absolute;
    bottom: -3px;
    left: 0;
    width: 40px;
    height: 2px;
    background: #57428D;
}

/* Workshop List */
.workshop-list {
    list-style: none;
    margin: 0;
    padding: 0;
}

.workshop-list li {
    position: relative;
    padding: 15px 0;
    border-bottom: 1px dashed rgba(0, 0, 0, 0.1);
}

.workshop-list li:last-child {
    padding-bottom: 0;
    border-bottom: none;
}

.workshop-list li::before {
    content: '•';
    color: #4965AD;
    font-size: 18px;
    margin-right: 10px;
}

/* Day Indicator */
.day-indicator {
    position: absolute;
    top: 0;
    right: 0;
    background: #F5F5F5;
    padding: 5px 15px;
    border-radius: 0 15px;
    font-size: 12px;
    font-weight: 700;
    color: #616161;
}

/* Mobile Full Width Class */
.mobile-full-width {
    width: 100% !important;
    max-width: 100% !important;
    box-sizing: border-box !important;
}

@media only screen and (max-width: 767px) {
    .mobile-full-width {
        width: 100% !important;
        max-width: 100% !important;
        box-sizing: border-box !important;
        margin-right: 0 !important;
        padding-right: 0 !important;
    }
}

/* Responsive Styles */
@media only screen and (max-width: 991px) {
    .schedule-section {
        padding: 70px 0;
    }
    
    .schedule-section .section-title h2 {
        font-size: 36px;
    }
    
    .schedule-section .section-title .venue-text {
        font-size: 16px;
    }
    
    .schedule-tabs .tab-btn {
        padding: 10px 20px;
        font-size: 14px;
    }
    
    .schedule-timeline::before {
        left: 25px;
    }
    
    .timeline-item {
        padding-left: 70px;
        margin-bottom: 25px;
        padding-bottom: 25px;
    }
    
    .timeline-content {
        padding: 22px 25px;
    }
    
    .indian-pattern {
        width: 150px;
        height: 150px;
    }
    
    .pattern-1 {
        top: 40px;
        right: 20px;
    }
    
    .pattern-2 {
        bottom: 40px;
        left: 20px;
    }
}

@media only screen and (max-width: 767px) {
    .schedule-section {
        padding: 50px 0;
    }
    
    .schedule-section .auto-container {
        width: 100%;
        padding: 0 15px;
    }
    
    .schedule-section .section-title h2 {
        font-size: 30px;
    }
    
    .schedule-section .section-title .title-text {
        font-size: 14px;
        padding: 8px 20px;
    }
    
    .schedule-section .section-title .venue-text {
        font-size: 15px;
    }
    
    .schedule-tabs {
        margin-bottom: 30px;
    }
    
    .schedule-tabs .tab-buttons {
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
        gap: 10px;
        padding: 8px;
        max-width: 95%;
        margin: 0 auto;
    }
    
    .schedule-tabs .tab-btn {
        padding: 8px 16px;
        font-size: 13px;
        min-width: 100px;
        text-align: center;
    }
    
    .schedule-timeline::before {
        left: 20px;
        width: 2px;
    }
    
    .timeline-item {
        padding-left: 60px;
        margin-bottom: 20px;
        padding-bottom: 20px;
    }
    
    .timeline-dot {
        left: 10px;
        top: 22px;
        width: 20px;
        height: 20px;
        border-width: 4px;
    }
    
    .timeline_dot::before {
        left: -12px;
        width: 12px;
    }
    
    .timeline-content {
        padding: 20px;
        border-left-width: 4px;
    }
    
    .event-time {
        padding: 4px 12px;
        margin-bottom: 12px;
    }
    
    .event-title {
        font-size: 18px;
        line-height: 1.3;
    }
    
    .event-title.featured {
        font-size: 20px;
    }
    
    .day-indicator {
        font-size: 11px;
        padding: 4px 12px;
    }
    
    .workshop-list li {
        padding: 12px 0;
        font-size: 14px;
        line-height: 1.4;
    }
    
    .indian-pattern {
        width: 100px;
        height: 100px;
    }
}

@media only screen and (max-width: 575px) {
    .schedule-section {
        padding: 40px 0;
    }
    
    .schedule-section .auto-container {
        padding: 0 10px;
        width: 100%;
    }
    
    .schedule-section .section-title {
        margin-bottom: 30px;
    }
    
    .schedule-section .section-title h2 {
        font-size: 26px;
    }
    
    .schedule-section .section-title .title-text {
        font-size: 13px;
        padding: 6px 16px;
        margin-bottom: 10px;
    }
    
    .schedule-section .section-title .venue-text {
        font-size: 14px;
    }
    
    .schedule-tabs .tab-buttons {
        width: 100%;
        flex-direction: column;
        gap: 8px;
        padding: 8px;
        border-radius: 15px;
        max-width: 95%;
    }
    
    .schedule-tabs .tab-btn {
        width: 100%;
        border-radius: 8px;
        padding: 10px;
        font-size: 13px;
    }
    
    .schedule-tabs .tab-btn small {
        font-size: 11px;
        opacity: 0.8;
        margin-left: 5px;
    }
    
    .schedule-content {
        width: 100%;
    }
    
    .schedule-timeline {
        width: 100%;
    }
    
    .schedule-timeline::before {
        left: 18px;
    }
    
    .timeline-item {
        padding-left: 45px;
        margin-bottom: 15px;
        padding-bottom: 15px;
        width: 100%;
    }
    
    .timeline-dot {
        left: 8px;
        width: 18px;
        height: 18px;
    }
    
    .timeline-content {
        padding: 15px;
        border-radius: 10px;
        width: 100%;
    }
    
    .event-title {
        font-size: 16px;
        word-wrap: break-word;
        width: 100%;
    }
    
    .event-title.featured {
        font-size: 17px;
    }
    
    .workshop-list li {
        padding: 10px 0;
        font-size: 13px;
    }
}

/* Critical Mobile Fixes with !important */
@media only screen and (max-width: 767px) {
    /* Container fixes */
    .auto-container {
        width: 100% !important;
        max-width: 100% !important;
        padding-left: 5px !important;
        padding-right: 5px !important;
    }
    
    /* Schedule section fixes */
    .schedule-section {
        width: 100% !important;
    }
    
    /* Content width fixes */
    .schedule-content {
        width: 100% !important;
    }
    
    /* Timeline width fixes */
    .schedule-timeline {
        width: 100% !important;
        max-width: 100% !important;
    }
    
    /* Timeline item fixes */
    .timeline-item {
        width: 100% !important;
        max-width: 100% !important;
        padding-left: 30px !important;
    }
    
    /* Timeline content fixes */
    .timeline-content {
        width: calc(100% - 15px) !important;
        max-width: calc(100% - 15px) !important;
        box-sizing: border-box !important;
        padding: 10px 15px !important;
    }
    
    /* Event title fixes */
    .event-title {
        width: 100% !important;
        max-width: 100% !important;
    }
}

/* Keep the timeline dot properly positioned */
@media only screen and (max-width: 767px) {
    .timeline-dot {
        left: 4px !important;
    }
    
    .schedule-timeline::before {
        left: 10px !important;
    }
}

/* Animation for schedule items */
.animate-schedule {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease;
}

.animate-schedule.show {
    opacity: 1;
    transform: translateY(0);
}

/* Special Features - Traditional Patterns */
.indian-pattern {
    position: absolute;
    width: 200px;
    height: 200px;
    background-size: contain;
    background-repeat: no-repeat;
    opacity: 0.05;
    z-index: 0;
}

.pattern-1 {
    top: 50px;
    right: 50px;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 120 120'%3E%3Cg fill='%2357428D'%3E%3Ccircle cx='60' cy='60' r='40'/%3E%3Ccircle cx='60' cy='60' r='35' fill='%23FFF'/%3E%3Ccircle cx='60' cy='60' r='30'/%3E%3Ccircle cx='60' cy='60' r='25' fill='%23FFF'/%3E%3Ccircle cx='60' cy='60' r='20'/%3E%3Ccircle cx='60' cy='60' r='15' fill='%23FFF'/%3E%3Ccircle cx='60' cy='60' r='10'/%3E%3Ccircle cx='60' cy='60' r='5' fill='%23FFF'/%3E%3C/g%3E%3C/svg%3E");
    animation: rotate 60s linear infinite;
}

.pattern-2 {
    bottom: 50px;
    left: 50px;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 120 120'%3E%3Cpath fill='%234965AD' d='M60 10L70 30H50L60 10zm0 100L70 90H50L60 110zM10 60L30 50v20L10 60zm100 0L90 50v20l20-10zM25 25l25 5-5 25L25 25zm70 0l-25 5 5 25 20-30zM25 95l25-5-5-25-20 30zm70 0l-25-5 5-25 20 30z'/%3E%3C/svg%3E");
    animation: rotate 80s linear infinite reverse;
}

@keyframes rotate {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

/* Loading effect for schedule items */
@keyframes shimmer {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}

.shimmer-effect {
    background: linear-gradient(90deg, #f0f0f0 25%, #f8f8f8 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
    border-radius: 10px;
    height: 20px;
    margin-bottom: 10px;
}

/* Additional Mobile and Tablet Enhancements */
@media (hover: hover) {
    /* Only apply hover effects on devices that support hover */
    .timeline-content:hover {
        transform: translateY(-5px);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    }
    
    .schedule-tabs .tab-btn:hover:not(.active-btn) {
        background-color: rgba(73, 101, 173, 0.05);
    }
}

/* Touch-friendly adjustments */
@media (pointer: coarse) {
    /* For touch devices */
    .schedule-tabs .tab-btn {
        min-height: 44px; /* Minimum touch target size */
        display: flex;
        align-items: center;
        justify-content: center;
    }
    
    .timeline-content {
        transition: none; /* Disable hover animations on touch devices */
    }
    
    /* Active state styling for touch feedback */
    .timeline-content:active {
        background-color: rgba(73, 101, 173, 0.05);
    }
}

/* iPad and similar tablet devices in portrait */
@media only screen and (min-width: 768px) and (max-width: 991px) {
    .schedule-section .auto-container {
        padding-left: 25px;
        padding-right: 25px;
    }
    
    .schedule-tabs .tab-buttons {
        max-width: 85%;
    }
    
    .timeline-item {
        transition: all 0.3s ease;
    }
    
    .timeline-item:active {
        transform: translateY(-2px);
    }
}

/* Small height mobile devices */
@media only screen and (max-height: 700px) and (max-width: 767px) {
    .schedule-section {
        padding: 30px 0;
    }
    
    .schedule-section .section-title {
        margin-bottom: 25px;
    }
    
    .schedule-tabs {
        margin-bottom: 25px;
    }
    
    .timeline-item {
        margin-bottom: 12px;
        padding-bottom: 12px;
    }
}

/* Ultra-small mobile devices */
@media only screen and (max-width: 360px) {
    .schedule-section .section-title h2 {
        font-size: 22px;
    }
    
    .schedule-section .section-title .title-text {
        font-size: 12px;
        padding: 5px 12px;
    }
    
    .schedule-section .section-title .venue-text {
        font-size: 13px;
    }
    
    .timeline-item {
        padding-left: 45px;
    }
    
    .timeline-dot {
        width: 16px;
        height: 16px;
        left: 12px;
    }
    
    .timeline-content {
        padding: 12px;
    }
    
    .event-title {
        font-size: 15px;
    }
    
    .event-time {
        padding: 3px 10px;
        font-size: 12px;
    }
    
    .workshop-list {
        padding-left: 5px;
    }
    
    .workshop-list li {
        padding: 8px 0;
        font-size: 12px;
    }
}

/* Very Small Mobile Devices (iPhone SE, etc.) */
@media only screen and (max-width: 375px) {
    .schedule-section {
        padding: 20px 0;
    }
    
    .schedule-section .auto-container {
        padding: 0 5px;
        max-width: 100%;
        margin: 0;
    }
    
    .schedule-content {
        width: 100%;
    }
    
    .schedule-timeline::before {
        left: 12px;
    }
    
    .timeline-item {
        padding-left: 30px;
        margin-bottom: 10px;
        padding-bottom: 10px;
    }
    
    .timeline-dot {
        left: 4px;
        top: 15px;
        width: 14px;
        height: 14px;
        border-width: 3px;
    }
    
    .timeline-content {
        padding: 10px 15px;
        width: calc(100% - 5px);
        margin-right: 0;
    }
    
    .event-title {
        font-size: 14px;
        line-height: 1.3;
        margin-right: 0;
        padding-right: 0;
    }
    
    .day-indicator {
        padding: 2px 6px;
        font-size: 10px;
    }
    
    .schedule-tabs .tab-btn {
        padding: 6px 10px;
        font-size: 12px;
        min-width: 75px;
    }
    
    .workshop-list li {
        font-size: 12px;
        line-height: 1.3;
        padding: 8px 0;
    }
}

/* iPhone SE Specific Fixes (matching the screenshot exactly) */
@media only screen and (width: 375px) and (height: 667px) {
    .schedule-section {
        padding: 15px 0;
    }
    
    .schedule-section .auto-container {
        padding-left: 0;
        padding-right: 0;
        width: 100%;
        max-width: 100%;
    }
    
    .schedule-content {
        width: 100%;
        max-width: 100%;
        overflow-x: hidden;
    }
    
    .schedule-block {
        width: 100%;
        padding: 0;
    }
    
    .schedule-timeline {
        width: 100%;
        padding: 0;
        max-width: 375px;
    }
    
    .schedule-timeline::before {
        left: 10px;
    }
    
    .timeline-item {
        padding-left: 30px;
        width: 100%;
        max-width: 100%;
    }
    
    .timeline-dot {
        left: 3px;
    }
    
    .timeline-content {
        width: calc(100% - 20px);
        max-width: 345px;
        padding: 10px;
    }
    
    .event-title {
        width: 100%;
        max-width: 100%;
    }
    
    /* Adjust container width */
    .auto-container {
        width: 100% !important;
        max-width: 100% !important;
        padding: 0 !important;
    }
    
    /* Fix for Day tabs */
    .schedule-tabs .tab-buttons {
        max-width: 100%;
    }
    
    /* Force full width */
    .schedule-section, .schedule-content, .schedule-block, .schedule-timeline, .timeline-item, .timeline-content {
        width: 100% !important;
        max-width: 100% !important;
        box-sizing: border-box !important;
    }
}
