/* International Faculty Modern Card Design */
.international-faculty-container {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 15px;
    padding: 40px 20px;
    max-width: 1200px;
    margin: 0 auto;
}

.faculty-card {
    background: linear-gradient(145deg, #ffffff 0%, #f8f9ff 100%);
    border-radius: 20px;
    padding: 30px 25px;
    text-align: center;
    box-shadow: 0 8px 30px rgba(73, 101, 173, 0.1);
    border: 1px solid rgba(73, 101, 173, 0.08);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    width: 100%;
    margin: 0 auto;
}

.faculty-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #4965AD 0%, #6B7FE8 50%, #4965AD 100%);
    transform: scaleX(0);
    transition: transform 0.4s ease;
}

.faculty-card:hover::before {
    transform: scaleX(1);
}

.faculty-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 40px rgba(73, 101, 173, 0.15);
    border-color: rgba(73, 101, 173, 0.2);
}

.faculty-profile {
    position: relative;
    margin-bottom: 20px;
}

.faculty-profile img {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    object-fit: cover;
    border: 4px solid #ffffff;
    box-shadow: 0 8px 25px rgba(73, 101, 173, 0.15);
    transition: all 0.4s ease;
    position: relative;
    z-index: 2;
    background-color: #f0f0f0;
}

/* Fallback for broken images */
.faculty-profile img[src=""],
.faculty-profile img:not([src]),
.faculty-profile img[src*="404"] {
    background: linear-gradient(135deg, #4965AD 0%, #6B7FE8 100%);
    display: flex;
    align-items: center;
    justify-content: center;
}

.faculty-profile img[src=""]:before,
.faculty-profile img:not([src]):before,
.faculty-profile img[src*="404"]:before {
    content: "👤";
    font-size: 40px;
    color: white;
}

.faculty-profile::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 110px;
    height: 110px;
    border-radius: 50%;
    background: linear-gradient(45deg, #4965AD, #6B7FE8);
    opacity: 0;
    transition: all 0.4s ease;
    z-index: 1;
}

.faculty-card:hover .faculty-profile::after {
    opacity: 0.1;
    width: 120px;
    height: 120px;
}

.faculty-card:hover .faculty-profile img {
    transform: scale(1.05);
    box-shadow: 0 12px 35px rgba(73, 101, 173, 0.25);
}

.faculty-name {
    font-size: 18px;
    font-weight: 600;
    color: #2c3e50;
    margin: 15px 0 8px;
    line-height: 1.3;
    transition: color 0.3s ease;
}

.faculty-card:hover .faculty-name {
    color: #4965AD;
}

.faculty-designation {
    font-size: 14px;
    color: #7f8c8d;
    font-weight: 500;
    margin: 0;
    position: relative;
    padding-top: 8px;
}

.faculty-designation::before {
    content: '';
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 30px;
    height: 2px;
    background: linear-gradient(90deg, #4965AD, #6B7FE8);
    border-radius: 1px;
    opacity: 0;
    transition: all 0.4s ease;
}

.faculty-card:hover .faculty-designation::before {
    opacity: 1;
    width: 50px;
}

/* Section Title Styling - Using existing committee-title class */

/* Responsive Design */
@media (max-width: 1024px) {
    .international-faculty-container {
        grid-template-columns: repeat(3, 1fr);
        gap: 12px;
        padding: 35px 15px;
    }
}

@media (max-width: 768px) {
    .international-faculty-container {
        grid-template-columns: repeat(2, 1fr);
        gap: 12px;
        padding: 30px 15px;
    }

    .faculty-card {
        padding: 25px 20px;
        max-width: none;
    }

    .faculty-profile img {
        width: 90px;
        height: 90px;
    }

    .faculty-profile::after {
        width: 100px;
        height: 100px;
    }

    .faculty-card:hover .faculty-profile::after {
        width: 110px;
        height: 110px;
    }

    .faculty-name {
        font-size: 17px;
    }


}

@media (max-width: 480px) {
    .international-faculty-container {
        grid-template-columns: 1fr;
        gap: 15px;
        padding: 25px 10px;
    }

    .faculty-card {
        padding: 20px 15px;
        max-width: 300px;
        margin: 0 auto;
    }

    .faculty-profile img {
        width: 80px;
        height: 80px;
    }

    .faculty-name {
        font-size: 16px;
    }

    .faculty-designation {
        font-size: 13px;
    }


}

/* Animation for cards on load */
.faculty-card {
    opacity: 0;
    transform: translateY(30px);
    animation: fadeInUp 0.6s ease forwards;
}

.faculty-card:nth-child(1) { animation-delay: 0.1s; }
.faculty-card:nth-child(2) { animation-delay: 0.2s; }
.faculty-card:nth-child(3) { animation-delay: 0.3s; }
.faculty-card:nth-child(4) { animation-delay: 0.4s; }
.faculty-card:nth-child(5) { animation-delay: 0.5s; }
.faculty-card:nth-child(6) { animation-delay: 0.6s; }
.faculty-card:nth-child(7) { animation-delay: 0.7s; }
.faculty-card:nth-child(8) { animation-delay: 0.8s; }

@keyframes fadeInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Hover effect for the entire container */
.international-faculty-container:hover .faculty-card:not(:hover) {
    opacity: 0.7;
    transform: scale(0.98);
}
