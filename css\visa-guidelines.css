/* Visa Guidelines Styles - Indian Formatting */
/* Color Scheme: Purple: #57428D, Blue: #4965AD, Light Pink: #FBD4D2 */

.visa-guidelines-section {
    padding: 40px 0;
    background: linear-gradient(135deg, #f8f9ff 0%, #f9f9f9 50%, #fff 100%);
    animation: fadeIn 0.8s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Main Header */
.visa-main-header {
    text-align: center;
    margin-bottom: 30px;
    padding: 25px 20px;
    background: linear-gradient(135deg, #57428D 0%, #4965AD 100%);
    border-radius: 8px;
    color: white;
    box-shadow: 0 4px 15px rgba(87, 66, 141, 0.2);
    transition: all 0.3s ease;
    cursor: default;
    animation: slideInDown 0.6s ease-out;
}

.visa-main-header:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(87, 66, 141, 0.3);
}

@keyframes slideInDown {
    from {
        opacity: 0;
        transform: translateY(-30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.visa-icon {
    font-size: 28px;
    margin-bottom: 10px;
    display: inline-block;
    margin-right: 10px;
}

.visa-icon::before {
    content: "\f2c2";
    font-family: "Font Awesome 5 Free";
    font-weight: 900;
    transition: transform 0.3s ease;
}

.visa-main-header:hover .visa-icon::before {
    transform: scale(1.1) rotate(5deg);
}

.visa-main-header h2 {
    font-size: 22px;
    font-weight: 600;
    margin: 0;
    line-height: 1.3;
    display: inline-block;
}

/* Info Card */
.visa-info-card {
    background: white;
    padding: 20px;
    border-radius: 6px;
    margin-bottom: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border-left: 4px solid #4965AD;
    transition: all 0.3s ease;
    animation: slideInLeft 0.7s ease-out;
    position: relative;
    overflow: hidden;
}

.visa-info-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.visa-info-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
    border-left: 4px solid #57428D;
}

.visa-info-card:hover::before {
    left: 100%;
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.visa-info-card p {
    font-size: 14px;
    line-height: 1.6;
    color: #333;
    margin-bottom: 12px;
    text-align: justify;
}

.visa-info-card p:last-child {
    margin-bottom: 0;
}

.important-date {
    background: #FBD4D2;
    padding: 15px;
    border-radius: 5px;
    margin-top: 15px;
    border-left: 3px solid #57428D;
}

.important-date strong {
    color: #57428D;
    font-size: 15px;
}

/* Disclaimer Card */
.disclaimer-card {
    background: #fff5f5;
    padding: 18px;
    border-radius: 6px;
    margin-bottom: 25px;
    border: 1px solid #FBD4D2;
    box-shadow: 0 2px 8px rgba(251, 212, 210, 0.2);
    transition: all 0.3s ease;
    animation: slideInRight 0.8s ease-out;
}

.disclaimer-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(251, 212, 210, 0.3);
    background: linear-gradient(135deg, #fff5f5 0%, #ffe6e6 100%);
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.disclaimer-header {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
}

.warning-icon {
    font-size: 20px;
    margin-right: 8px;
}

.warning-icon::before {
    content: "\f071";
    font-family: "Font Awesome 5 Free";
    font-weight: 900;
    color: #f39c12;
    transition: all 0.3s ease;
}

.disclaimer-card:hover .warning-icon::before {
    transform: scale(1.2) rotate(10deg);
    color: #e67e22;
}

.disclaimer-header h3 {
    color: #57428D;
    font-size: 16px;
    font-weight: 600;
    margin: 0;
}

.disclaimer-card p {
    color: #555;
    line-height: 1.5;
    margin: 0;
    font-size: 13px;
    text-align: justify;
}

/* Steps Section */
.steps-section {
    margin-bottom: 30px;
    animation: fadeInUp 0.9s ease-out;
}

.steps-header {
    text-align: center;
    margin-bottom: 20px;
    padding: 15px 20px;
    background: linear-gradient(135deg, #4965AD 0%, #57428D 100%);
    border-radius: 6px;
    color: white;
    transition: all 0.3s ease;
}

.steps-header:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(73, 101, 173, 0.3);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.check-icon {
    font-size: 18px;
    margin-right: 8px;
}

.check-icon::before {
    content: "\f058";
    font-family: "Font Awesome 5 Free";
    font-weight: 900;
    color: #27ae60;
    transition: all 0.3s ease;
}

.steps-header:hover .check-icon::before {
    transform: scale(1.2);
    color: #2ecc71;
}

.steps-header h2 {
    font-size: 18px;
    font-weight: 600;
    margin: 0;
    display: inline-block;
}

.steps-container {
    display: grid;
    gap: 15px;
}

.step-card {
    background: white;
    border-radius: 6px;
    padding: 18px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: flex-start;
    transition: all 0.3s ease;
    border-left: 3px solid #4965AD;
    opacity: 0;
    animation: slideInUp 0.6s ease-out forwards;
    position: relative;
    overflow: hidden;
}

.step-card:nth-child(1) { animation-delay: 0.1s; }
.step-card:nth-child(2) { animation-delay: 0.2s; }
.step-card:nth-child(3) { animation-delay: 0.3s; }
.step-card:nth-child(4) { animation-delay: 0.4s; }
.step-card:nth-child(5) { animation-delay: 0.5s; }
.step-card:nth-child(6) { animation-delay: 0.6s; }

.step-card::after {
    content: '';
    position: absolute;
    top: 0;
    right: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(73, 101, 173, 0.1), transparent);
    transition: right 0.5s;
}

.step-card:hover {
    transform: translateY(-3px) scale(1.02);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    border-left: 3px solid #57428D;
}

.step-card:hover::after {
    right: 100%;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.step-number {
    background: linear-gradient(135deg, #57428D 0%, #4965AD 100%);
    color: white;
    width: 35px;
    height: 35px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    font-weight: 600;
    margin-right: 15px;
    flex-shrink: 0;
    transition: all 0.3s ease;
    position: relative;
}

.step-number::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(135deg, #57428D 0%, #4965AD 100%);
    border-radius: 50%;
    z-index: -1;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.step-card:hover .step-number {
    transform: scale(1.1) rotate(360deg);
    box-shadow: 0 4px 15px rgba(87, 66, 141, 0.4);
}

.step-card:hover .step-number::before {
    opacity: 0.3;
}

.step-content {
    flex: 1;
}

.step-content h3 {
    color: #57428D;
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 8px;
}

.step-content p {
    color: #555;
    line-height: 1.5;
    margin-bottom: 8px;
    font-size: 13px;
}

.step-content ul {
    margin: 0;
    padding-left: 18px;
}

.step-content li {
    color: #555;
    line-height: 1.5;
    margin-bottom: 6px;
    font-size: 13px;
}

.note-box {
    background: #FBD4D2;
    padding: 12px;
    border-radius: 5px;
    margin-top: 12px;
    border-left: 3px solid #57428D;
}

.note-box strong {
    color: #57428D;
}

.visa-link {
    color: #4965AD;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.2s ease;
}

.visa-link:hover {
    color: #57428D;
    text-decoration: underline;
}

/* Documents Section */
.documents-section {
    margin-bottom: 30px;
    animation: fadeInUp 1s ease-out;
}

.documents-header {
    text-align: center;
    margin-bottom: 20px;
    padding: 15px 20px;
    background: linear-gradient(135deg, #57428D 0%, #4965AD 100%);
    border-radius: 6px;
    color: white;
    transition: all 0.3s ease;
}

.documents-header:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(87, 66, 141, 0.3);
}

.doc-icon {
    font-size: 18px;
    margin-right: 8px;
}

.doc-icon::before {
    content: "\f46d";
    font-family: "Font Awesome 5 Free";
    font-weight: 900;
    color: #3498db;
    transition: all 0.3s ease;
}

.documents-header:hover .doc-icon::before {
    transform: scale(1.2) rotateY(180deg);
    color: #2980b9;
}

.documents-header h2 {
    font-size: 18px;
    font-weight: 600;
    margin: 0;
    display: inline-block;
}

.documents-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 15px;
}

.doc-category {
    background: white;
    padding: 18px;
    border-radius: 6px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    border-left: 3px solid #4965AD;
    opacity: 0;
    animation: fadeInScale 0.6s ease-out forwards;
}

.doc-category:nth-child(1) { animation-delay: 0.1s; }
.doc-category:nth-child(2) { animation-delay: 0.2s; }
.doc-category:nth-child(3) { animation-delay: 0.3s; }

.doc-category:hover {
    transform: translateY(-3px) scale(1.02);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
    border-left: 3px solid #57428D;
}

@keyframes fadeInScale {
    from {
        opacity: 0;
        transform: scale(0.9) translateY(20px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

.doc-category.full-width {
    grid-column: 1 / -1;
}

.doc-category h3 {
    color: #57428D;
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 12px;
    padding-bottom: 8px;
    border-bottom: 1px solid #FBD4D2;
}

.doc-category p {
    color: #555;
    line-height: 1.5;
    margin-bottom: 10px;
    font-size: 13px;
    text-align: justify;
}

.doc-category ul {
    margin: 0;
    padding-left: 18px;
}

.doc-category li {
    color: #555;
    line-height: 1.5;
    margin-bottom: 6px;
    font-size: 13px;
}

/* Apply Section */
.apply-section {
    margin-bottom: 30px;
    animation: fadeInUp 1.1s ease-out;
}

.apply-header {
    text-align: center;
    margin-bottom: 20px;
    padding: 15px 20px;
    background: linear-gradient(135deg, #4965AD 0%, #57428D 100%);
    border-radius: 6px;
    color: white;
    transition: all 0.3s ease;
}

.apply-header:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(73, 101, 173, 0.3);
}

.plane-icon {
    font-size: 18px;
    margin-right: 8px;
}

.plane-icon::before {
    content: "\f072";
    font-family: "Font Awesome 5 Free";
    font-weight: 900;
    color: #e74c3c;
    transition: all 0.3s ease;
}

.apply-header:hover .plane-icon::before {
    transform: scale(1.2) translateX(5px);
    color: #c0392b;
}

.apply-header h2 {
    font-size: 18px;
    font-weight: 600;
    margin: 0;
    display: inline-block;
}

.apply-section > p {
    font-size: 14px;
    line-height: 1.6;
    color: #555;
    margin-bottom: 20px;
    text-align: center;
}

.exemption-card {
    background: white;
    padding: 18px;
    border-radius: 6px;
    margin-bottom: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border-left: 3px solid #4965AD;
    transition: all 0.3s ease;
    animation: slideInLeft 1.2s ease-out;
}

.exemption-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
    border-left: 3px solid #57428D;
}

.exemption-card h3 {
    color: #57428D;
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 12px;
}

.exemption-card ul {
    margin: 0;
    padding-left: 18px;
}

.exemption-card li {
    color: #555;
    line-height: 1.5;
    margin-bottom: 8px;
    font-size: 13px;
}

.process-section {
    margin-bottom: 20px;
}

.process-section h3 {
    color: #57428D;
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 12px;
    text-align: center;
}

.process-section > p {
    color: #555;
    line-height: 1.6;
    margin-bottom: 15px;
    text-align: center;
    font-size: 14px;
}

.visa-types {
    display: grid;
    gap: 15px;
}

.visa-type-card {
    background: white;
    padding: 18px;
    border-radius: 6px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border-left: 3px solid #4965AD;
    transition: all 0.3s ease;
    opacity: 0;
    animation: bounceIn 0.8s ease-out forwards;
}

.visa-type-card:nth-child(1) { animation-delay: 0.2s; }
.visa-type-card:nth-child(2) { animation-delay: 0.4s; }
.visa-type-card:nth-child(3) { animation-delay: 0.6s; }

.visa-type-card:hover {
    transform: translateY(-3px) scale(1.02);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    border-left: 3px solid #57428D;
}

@keyframes bounceIn {
    0% {
        opacity: 0;
        transform: scale(0.3) translateY(30px);
    }
    50% {
        opacity: 0.7;
        transform: scale(1.05) translateY(-10px);
    }
    100% {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

.visa-type-card h4 {
    color: #57428D;
    font-size: 15px;
    font-weight: 600;
    margin-bottom: 10px;
}

.visa-type-card p {
    color: #555;
    line-height: 1.5;
    margin-bottom: 10px;
    font-size: 13px;
}

.visa-type-card ul {
    margin: 0;
    padding-left: 18px;
}

.visa-type-card li {
    color: #555;
    line-height: 1.5;
    margin-bottom: 6px;
    font-size: 13px;
}

.timeline-card {
    background: #FBD4D2;
    padding: 18px;
    border-radius: 6px;
    margin-bottom: 20px;
    border: 1px solid #FBD4D2;
    transition: all 0.3s ease;
    animation: slideInRight 1.3s ease-out;
}

.timeline-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(251, 212, 210, 0.4);
    background: linear-gradient(135deg, #FBD4D2 0%, #f8c8c6 100%);
}

.timeline-card h3 {
    color: #57428D;
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 12px;
    text-align: center;
}

.timeline-card ul {
    margin: 0;
    padding-left: 18px;
}

.timeline-card li {
    color: #555;
    line-height: 1.5;
    margin-bottom: 8px;
    font-size: 13px;
}

.reminders-card {
    background: white;
    padding: 18px;
    border-radius: 6px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border-left: 3px solid #4965AD;
    transition: all 0.3s ease;
    animation: fadeInUp 1.4s ease-out;
}

.reminders-card:hover {
    transform: translateY(-3px) scale(1.01);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    border-left: 3px solid #57428D;
}

.reminders-header {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    justify-content: center;
}

.reminders-header h3 {
    color: #57428D;
    font-size: 16px;
    font-weight: 600;
    margin: 0;
}

.reminders-card ul {
    margin: 0;
    padding-left: 18px;
}

.reminders-card li {
    color: #555;
    line-height: 1.5;
    margin-bottom: 8px;
    font-size: 13px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .visa-guidelines-section {
        padding: 30px 0;
    }
    
    .visa-main-header {
        padding: 20px 15px;
        margin-bottom: 25px;
    }
    
    .visa-main-header h2 {
        font-size: 18px;
    }
    
    .visa-icon {
        font-size: 24px;
    }
    
    .visa-info-card,
    .disclaimer-card,
    .step-card,
    .doc-category,
    .exemption-card,
    .visa-type-card,
    .timeline-card,
    .reminders-card {
        padding: 15px;
    }
    
    .step-card {
        flex-direction: column;
        text-align: center;
    }
    
    .step-number {
        margin-right: 0;
        margin-bottom: 12px;
    }
    
    .documents-grid {
        grid-template-columns: 1fr;
    }
    
    .steps-header h2,
    .documents-header h2,
    .apply-header h2 {
        font-size: 16px;
    }
    
    .step-content h3,
    .doc-category h3,
    .exemption-card h3,
    .visa-type-card h4,
    .timeline-card h3,
    .reminders-header h3 {
        font-size: 15px;
    }
}

@media (max-width: 480px) {
    .visa-main-header h2 {
        font-size: 16px;
    }
    
    .steps-header,
    .documents-header,
    .apply-header {
        padding: 12px 15px;
    }
    
    .steps-header h2,
    .documents-header h2,
    .apply-header h2 {
        font-size: 15px;
    }
    
    .check-icon,
    .doc-icon,
    .plane-icon {
        font-size: 16px;
        margin-right: 6px;
    }
    
    .visa-info-card,
    .disclaimer-card,
    .step-card,
    .doc-category,
    .exemption-card,
    .visa-type-card,
    .timeline-card,
    .reminders-card {
        padding: 12px;
    }
} 