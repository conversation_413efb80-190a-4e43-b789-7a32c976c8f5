// ISAR 2026 Schedule JavaScript

document.addEventListener("DOMContentLoaded", function() {
    // Tab functionality
    const tabButtons = document.querySelectorAll('.tab-btn');
    const scheduleBlocks = document.querySelectorAll('.schedule-block');

    // Set first tab as active by default
    if(tabButtons.length > 0 && scheduleBlocks.length > 0) {
        tabButtons[0].classList.add('active-btn');
        scheduleBlocks[0].classList.add('active-tab');
    }

    // Add click event to tab buttons
    tabButtons.forEach(button => {
        button.addEventListener('click', () => {
            const targetTab = button.getAttribute('data-tab');
            
            // Remove active class from all buttons and tabs
            tabButtons.forEach(btn => btn.classList.remove('active-btn'));
            scheduleBlocks.forEach(block => block.classList.remove('active-tab'));
            
            // Add active class to clicked button and corresponding tab
            button.classList.add('active-btn');
            const targetBlock = document.querySelector(`.schedule-block[data-tab="${targetTab}"]`);
            targetBlock.classList.add('active-tab');
            
            // For mobile - scroll to the tab content with a small delay and offset
            if (window.innerWidth < 768) {
                setTimeout(() => {
                    const tabContentTop = document.querySelector('.schedule-tabs').getBoundingClientRect().bottom;
                    const scrollPosition = window.scrollY + tabContentTop;
                    
                    window.scrollTo({
                        top: scrollPosition - 20,
                        behavior: 'smooth'
                    });
                }, 100);
            }
        });
    });
    
    // Swipe functionality for mobile
    let touchStartX = 0;
    let touchEndX = 0;
    let currentTabIndex = 0;
    
    const handleSwipe = () => {
        // Get current active tab index
        tabButtons.forEach((btn, index) => {
            if (btn.classList.contains('active-btn')) {
                currentTabIndex = index;
            }
        });
        
        // Minimum swipe distance (in px)
        const swipeThreshold = 80;
        const swipeDistance = touchEndX - touchStartX;
        
        if (swipeDistance > swipeThreshold && currentTabIndex > 0) {
            // Swipe right, go to previous tab
            tabButtons[currentTabIndex - 1].click();
        } else if (swipeDistance < -swipeThreshold && currentTabIndex < tabButtons.length - 1) {
            // Swipe left, go to next tab
            tabButtons[currentTabIndex + 1].click();
        }
    };
    
    // Only add swipe detection on touch devices
    if ('ontouchstart' in window) {
        const scheduleContent = document.querySelector('.schedule-content');
        
        scheduleContent.addEventListener('touchstart', (e) => {
            touchStartX = e.changedTouches[0].screenX;
        });
        
        scheduleContent.addEventListener('touchend', (e) => {
            touchEndX = e.changedTouches[0].screenX;
            handleSwipe();
        });
    }

    // Animation on scroll
    const animateElements = document.querySelectorAll('.animate-schedule');
    
    function checkInView() {
        animateElements.forEach(element => {
            const elementTop = element.getBoundingClientRect().top;
            const elementBottom = element.getBoundingClientRect().bottom;
            
            // Check if element is in viewport
            if (elementTop <= window.innerHeight * 0.85 && elementBottom >= 0) {
                element.classList.add('show');
            }
        });
    }
    
    // Initial check on load
    checkInView();
    
    // Check on scroll with throttling for better performance
    let scrollTimeout;
    window.addEventListener('scroll', () => {
        if (!scrollTimeout) {
            scrollTimeout = setTimeout(() => {
                checkInView();
                scrollTimeout = null;
            }, 100);
        }
    });
    
    // Handle device orientation changes
    window.addEventListener('orientationchange', () => {
        setTimeout(checkInView, 300);
    });
    
    // Make sure all tabs are properly sized on window resize
    window.addEventListener('resize', () => {
        setTimeout(() => {
            // Force recalculation of heights
            scheduleBlocks.forEach(block => {
                if (block.classList.contains('active-tab')) {
                    block.style.display = 'none';
                    setTimeout(() => {
                        block.style.display = '';
                    }, 10);
                }
            });
        }, 300);
    });
});
