/* Responsive Footer CSS */
/* Default styling for all screen sizes */
.footer-bottom .powered-by-text,
.footer-bottom .copyright-text {
    display: flex;
    align-items: center;
    height: 100%;
    padding: 21px 0px;
}

@media (max-width: 767px) {
    .footer-bottom .inner-container {
        flex-direction: column;
        display: flex;
        align-items: center;
        text-align: center;
        padding: 5px 0;
    }
    .footer-bottom .powered-by-text,
    .footer-bottom .copyright-text {
        float: none !important;
        width: 100%;
        justify-content: center;
        margin: 0;
        text-align: center;
        padding: 1px 0;
        line-height: 1.2;
        height: auto;
    }
    .footer-bottom .copyright-text {
        margin-top: 0;
    }
    .pull-left, .pull-right {
        float: none !important;
        display: block;
        text-align: center;
        margin: 0 auto;
    }
    
    /* Ensure text is actually centered */
    .footer-bottom {
        text-align: center;
        padding: 5px 0;
    }
    
    .footer-bottom .text-center {
        text-align: center !important;
    }
} 