/* Modern Accommodation Hero Section */
.accommodation-hero {
    padding: 100px 0 80px;
    background: linear-gradient(135deg, #f9f9ff 0%, #f0f8ff 100%);
    position: relative;
    overflow: hidden;
}

.accommodation-hero:before {
    content: '';
    position: absolute;
    top: -100px;
    right: -100px;
    width: 300px;
    height: 300px;
    background: rgba(73, 101, 173, 0.05);
    border-radius: 50%;
}

.accommodation-hero:after {
    content: '';
    position: absolute;
    bottom: -100px;
    left: -100px;
    width: 300px;
    height: 300px;
    background: rgba(73, 101, 173, 0.05);
    border-radius: 50%;
}

.accommodation-hero-content {
    position: relative;
    z-index: 1;
}

.accommodation-hero-text {
    padding-right: 30px;
}

.section-subtitle {
    font-size: 16px;
    font-weight: 600;
    color: #4965AD;
    text-transform: uppercase;
    letter-spacing: 1.5px;
    margin-bottom: 15px;
    display: inline-block;
    position: relative;
}

/* Removed underline
.section-subtitle:after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 40px;
    height: 2px;
    background: #4965AD;
} */

.hero-title {
    font-size: 42px;
    font-weight: 700;
    color: #333;
    margin-bottom: 20px;
    line-height: 1.2;
}

.hero-description {
    font-size: 18px;
    color: #666;
    line-height: 1.6;
    margin-bottom: 30px;
}

.hero-features {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 30px;
}

.feature-item {
    display: flex;
    align-items: center;
    margin-right: 30px;
    margin-bottom: 15px;
}

.feature-item i {
    font-size: 20px;
    color: #4965AD;
    margin-right: 10px;
}

.feature-item span {
    font-size: 16px;
    color: #555;
    font-weight: 500;
}

.hero-cta {
    margin-top: 10px;
}

.accommodation-hero-image {
    position: relative;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 15px 30px rgba(0,0,0,0.15);
}

.main-image {
    width: 100%;
    height: 400px;
    object-fit: cover;
    display: block;
    transition: transform 0.5s ease;
}

.accommodation-hero-image:hover .main-image {
    transform: scale(1.05);
}

.image-badge {
    position: absolute;
    top: 20px;
    right: 0;
    background: #4965AD;
    color: #fff;
    padding: 10px 20px;
    border-radius: 4px 0 0 4px;
    box-shadow: 0 5px 15px rgba(73, 101, 173, 0.3);
    display: flex;
    flex-direction: column;
}

.badge-text {
    font-size: 16px;
    font-weight: 700;
    margin-bottom: 2px;
}

.badge-discount {
    font-size: 14px;
    opacity: 0.9;
}

/* Responsive Styles */
@media (max-width: 991px) {
    .accommodation-hero {
        padding: 80px 0 60px;
    }

    .accommodation-hero-text {
        padding-right: 0;
        margin-bottom: 40px;
        text-align: center;
    }

    .section-subtitle:after {
        left: 50%;
        transform: translateX(-50%);
    }

    .hero-features {
        justify-content: center;
    }

    .feature-item {
        margin: 0 15px 15px;
    }
}

@media (max-width: 767px) {
    .accommodation-hero {
        padding: 60px 0 40px;
    }

    .hero-title {
        font-size: 32px;
    }

    .hero-description {
        font-size: 16px;
    }

    .main-image {
        height: 300px;
    }
}

/* New Accommodation Styles */

/* Intro Section */
.accommodation-intro {
    padding: 100px 0 70px;
    background: linear-gradient(135deg, #f9f9ff 0%, #f0f8ff 100%);
    position: relative;
    overflow: hidden;
}

.accommodation-intro:before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 300px;
    height: 300px;
    background: rgba(26, 35, 126, 0.05);
    border-radius: 50%;
    transform: translate(150px, -150px);
}

.accommodation-intro:after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 200px;
    height: 200px;
    background: rgba(26, 35, 126, 0.05);
    border-radius: 50%;
    transform: translate(-100px, 100px);
}

.featured-title {
    position: relative;
    margin-bottom: 40px;
}

.welcome-heading {
    font-size: 42px;
    font-weight: 700;
    color: #1a237e;
    margin-bottom: 20px;
    text-transform: uppercase;
    letter-spacing: 1px;
    position: relative;
    display: inline-block;
}

.welcome-heading:after {
    content: '';
    position: absolute;
    bottom: -12px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 3px;
    background: #e91e63;
}

.premium-text {
    font-size: 20px;
    color: #555;
    margin-top: 25px;
    line-height: 1.6;
    font-weight: 400;
}

.accommodation-intro .btn-box {
    margin-top: 40px;
    position: relative;
    z-index: 5;
}

/* Button styling enhancements */
.accommodation-btn {
    padding: 15px 40px !important;
    border-radius: 50px !important;
    background: #4965AD !important;
    border: 2px solid #4965AD !important;
    box-shadow: 0 5px 15px rgba(73, 101, 173, 0.2) !important;
    transition: all 0.3s ease !important;
}

.accommodation-btn .btn-title {
    font-size: 16px !important;
    font-weight: 600 !important;
    letter-spacing: 0.5px !important;
    color: #ffffff !important;
    transition: all 0.3s ease !important;
}

.accommodation-btn:hover {
    background: #fff !important;
    border-color: #4965AD !important;
    box-shadow: 0 8px 20px rgba(73, 101, 173, 0.3) !important;
    transform: translateY(-3px) !important;
}

.accommodation-btn:hover .btn-title {
    color: #4965AD !important;
}

.accommodation-btn .btn-title i {
    margin-left: 8px;
    transition: transform 0.3s ease;
}

.accommodation-btn:hover .btn-title i {
    transform: translateX(5px);
}

/* Hotels Carousel */
.hotels-carousel-section {
    padding: 80px 0 100px;
    background: linear-gradient(135deg, #ffffff 0%, #f5f7ff 100%);
    position: relative;
}

.hotels-carousel-section:before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 200px;
    background: linear-gradient(to bottom, rgba(245, 247, 255, 0.7), transparent);
}

.section-heading {
    font-size: 36px;
    font-weight: 700;
    color: #4965AD;
    margin-bottom: 15px;
    text-transform: uppercase;
    letter-spacing: 1px;
    position: relative;
    display: block;
    text-align: center;
    text-decoration: none;
}

.heading-underline {
    width: 80px;
    height: 3px;
    background: #e91e63;
    margin: 10px auto 20px;
    display: block;
}

.section-subtitle {
    font-size: 18px;
    color: #555;
    margin-top: 25px;
    letter-spacing: 0.5px;
    max-width: 700px;
    margin-left: auto;
    margin-right: auto;
    display: block;
    clear: both;
    text-decoration: none;
}

.hotels-carousel-section .section-title {
    margin-bottom: 50px;
    position: relative;
    z-index: 1;
}

/* Decorative elements */
.hotels-carousel-section .section-title:before {
    content: '';
    position: absolute;
    top: -20px;
    left: 50%;
    transform: translateX(-50%);
    width: 120px;
    height: 120px;
    background: rgba(26, 35, 126, 0.03);
    border-radius: 50%;
    z-index: -1;
}

.hotels-carousel-section .owl-nav {
    margin-top: 30px;
}

.hotel-carousel {
    margin-top: 40px;
}

.hotel-carousel .owl-nav {
    position: absolute;
    top: -70px;
    right: 0;
}

.hotel-carousel .owl-nav button {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #f0f0f0 !important;
    margin-left: 10px;
    transition: all 0.3s ease;
}

.hotel-carousel .owl-nav button:hover {
    background: #1a237e !important;
}

.hotel-carousel .owl-nav button i {
    color: #1a237e;
    font-size: 18px;
}

.hotel-carousel .owl-nav button:hover i {
    color: #fff;
}

.hotel-item {
    margin: 10px;
}

.hotel-item .inner-box {
    background: #fff;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 0 15px rgba(0,0,0,0.08);
    transition: all 0.3s ease;
}

.hotel-item .inner-box:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0,0,0,0.12);
}

.hotel-item .image-box {
    position: relative;
    overflow: hidden;
    height: 220px;
}

.hotel-item .image-box img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.hotel-item:hover .image-box img {
    transform: scale(1.05);
}

.hotel-item .hotel-name {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    padding: 15px;
    background: linear-gradient(to top, rgba(0,0,0,0.8), transparent);
}

.hotel-item .hotel-name h3 {
    color: #fff;
    font-size: 20px;
    margin-bottom: 5px;
}

.hotel-item .hotel-name p {
    color: rgba(255,255,255,0.8);
    font-size: 14px;
    margin: 0;
}

.hotel-item .lower-content {
    padding: 20px;
}

.hotel-item .distance-info {
    margin-bottom: 15px;
}

.hotel-item .distance-item {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}

.hotel-item .distance-item:last-child {
    margin-bottom: 0;
}

.hotel-item .distance-item i {
    font-size: 16px;
    color: #1a237e;
    margin-right: 10px;
    min-width: 20px;
    text-align: center;
}

.hotel-item .distance-item .label {
    font-size: 13px;
    color: #777;
    display: block;
}

.hotel-item .distance-item .value {
    font-size: 14px;
    color: #333;
    font-weight: 500;
    display: block;
}

.hotel-item .price-info {
    padding-top: 15px;
    border-top: 1px solid #eee;
}

.hotel-item .price-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.hotel-item .price-row:last-child {
    margin-bottom: 0;
}

.hotel-item .room-type {
    font-size: 14px;
    color: #555;
}

.hotel-item .price {
    font-size: 16px;
    color: #1a237e;
    font-weight: 600;
}

/* Booking Information Section */
.booking-info-section {
    padding: 80px 0;
    background-color: #f8f9fa;
}

.booking-info, .booking-policy {
    background: #fff;
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 0 15px rgba(0,0,0,0.08);
    height: 100%;
}

.booking-info h3, .booking-policy h3 {
    color: #1a237e;
    font-size: 22px;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
}

.features-list, .policy-list {
    list-style: none;
    padding: 0;
    margin: 0 0 30px;
}

.features-list li, .policy-list li {
    margin-bottom: 15px;
    padding-left: 30px;
    position: relative;
    color: #555;
    font-size: 15px;
    line-height: 1.6;
}

.features-list li:last-child, .policy-list li:last-child {
    margin-bottom: 0;
}

.features-list li i, .policy-list li i {
    position: absolute;
    left: 0;
    top: 3px;
    color: #1a237e;
}

.booking-cta {
    background: linear-gradient(135deg, #4965AD, #3A4F87);
    padding: 30px;
    border-radius: 10px;
    color: #fff;
    margin-top: 30px;
}

.booking-cta h3 {
    color: #fff;
    border-bottom: none;
    margin-bottom: 10px;
}

.booking-cta p {
    color: rgba(255,255,255,0.8);
    margin-bottom: 20px;
}

.booking-cta .theme-btn {
    background: #fff;
    color: #4965AD;
}

.booking-cta .theme-btn:hover {
    background: rgba(255,255,255,0.9);
}

.booking-cta .note {
    margin-top: 20px;
    font-size: 13px;
    opacity: 0.8;
}

/* Responsive Styles */
@media (max-width: 991px) {
    .booking-policy {
        margin-top: 30px;
    }

    .hotel-carousel .owl-nav {
        top: -60px;
    }
}

@media (max-width: 767px) {
    .accommodation-intro,
    .hotels-carousel-section,
    .booking-info-section {
        padding: 50px 0;
    }

    .hotel-carousel .owl-nav {
        position: static;
        margin-top: 20px;
        text-align: center;
    }

    .booking-cta {
        padding: 20px;
    }
}

/* Fix for button hover */
.theme-btn:hover .btn-title {
    color: #ffffff !important;
    opacity: 1;
    visibility: visible;
}

.theme-btn.btn-style-one:hover {
    background: #1a237e;
}

.theme-btn.btn-style-one:hover .btn-title {
    color: #ffffff !important;
}

.booking-cta .theme-btn.btn-style-one:hover {
    background: rgba(255, 255, 255, 0.9);
}

.booking-cta .theme-btn.btn-style-one:hover .btn-title {
    color: #4965AD !important;
}

.btn-style-two:hover {
    background: #1a237e;
}

.btn-style-two:hover .btn-title {
    color: #ffffff !important;
}

/* New button styling approach */
.fixed-btn {
    display: inline-block;
    padding: 15px 40px;
    border-radius: 50px;
    background-color: #4965AD;
    color: #ffffff;
    text-decoration: none;
    font-size: 16px;
    font-weight: 600;
    letter-spacing: 0.5px;
    border: 2px solid #4965AD;
    box-shadow: 0 5px 15px rgba(73, 101, 173, 0.2);
    transition: all 0.3s ease;
    position: relative;
    overflow: visible;
}

.fixed-btn:hover {
    background-color: #ffffff;
    color: #4965AD;
    box-shadow: 0 8px 20px rgba(73, 101, 173, 0.3);
    transform: translateY(-3px);
}

.fixed-btn .btn-content {
    position: relative;
    display: inline-block;
    color: inherit;
    z-index: 5;
    transition: color 0.3s ease;
}

.fixed-btn:hover .btn-content {
    color: #4965AD;
}

.fixed-btn .btn-content i {
    margin-left: 8px;
    transition: transform 0.3s ease;
}

.fixed-btn:hover .btn-content i {
    transform: translateX(5px);
}

/* Ensure button content is always visible */
.fixed-btn .btn-content,
.fixed-btn:hover .btn-content,
.fixed-btn:focus .btn-content,
.fixed-btn:active .btn-content {
    opacity: 1 !important;
    visibility: visible !important;
    display: inline-block !important;
}