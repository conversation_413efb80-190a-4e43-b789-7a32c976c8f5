/* Critical CSS - These styles are essential for initial page rendering */

/* Basic resets and body styles */
body {
    margin: 0;
    padding: 0;
    font-family: 'Poppins', sans-serif;
    font-size: 16px;
    line-height: 1.5;
    color: #333;
    background-color: #fff;
}

/* Header and navigation */
.main-header {
    position: relative;
    left: 0px;
    top: 0px;
    width: 100%;
    z-index: 999;
    background-color: #fff;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

.header-span {
    position: relative;
    display: block;
    width: 100%;
    height: 100px;
}

.header-top {
    background-color: #11204A;
    padding: 10px 0;
    color: #fff;
}

.main-box {
    position: relative;
    padding: 0 15px;
}

.navigation {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
}

.navigation li {
    position: relative;
    margin-right: 25px;
}

.navigation li a {
    position: relative;
    display: block;
    color: #333;
    text-align: left;
    padding: 10px 0;
    font-size: 16px;
    font-weight: 500;
    text-decoration: none;
}

/* Auto container - Used throughout the site */
.auto-container {
    position: static;
    max-width: 1200px;
    padding: 0px 15px;
    margin: 0 auto;
}

/* Page title */
.page-title {
    position: relative;
    padding: 100px 0;
    background-position: center;
    background-size: cover;
    background-repeat: no-repeat;
    color: #fff;
    text-align: center;
}

.page-title:before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
}

.page-title h1 {
    position: relative;
    font-size: 48px;
    font-weight: 700;
    margin-bottom: 15px;
}

.page-title .bread-crumb {
    position: relative;
    display: flex;
    justify-content: center;
    list-style: none;
    margin: 0;
    padding: 0;
}

.page-title .bread-crumb li {
    position: relative;
    margin-right: 10px;
    padding-right: 15px;
    color: #fff;
    font-size: 16px;
    font-weight: 500;
}

.page-title .bread-crumb li:after {
    content: '-';
    position: absolute;
    right: 0;
    top: 0;
}

.page-title .bread-crumb li:last-child {
    margin-right: 0;
    padding-right: 0;
}

.page-title .bread-crumb li:last-child:after {
    display: none;
}

/* Intro section */
.intro-section-container {
    position: relative;
    padding: 80px 0;
    overflow: hidden;
}

.intro-heading {
    font-size: 36px;
    font-weight: 700;
    margin-bottom: 20px;
    color: #11204A;
}

.intro-paragraph {
    font-size: 18px;
    line-height: 1.6;
    max-width: 800px;
    margin: 0 auto;
    color: #666;
}

/* Place cards */
.places-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 25px;
    margin-top: 30px;
}

.place-card {
    position: relative;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    background-color: #fff;
}

.place-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.place-img {
    position: relative;
    overflow: hidden;
    height: 220px;
}

.place-img img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.place-category {
    position: absolute;
    top: 15px;
    right: 15px;
    padding: 5px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    color: #fff;
    z-index: 1;
    background-color: #11204A;
}

.place-info {
    padding: 20px;
}

.place-title {
    font-size: 20px;
    font-weight: 700;
    margin-bottom: 5px;
    color: #11204A;
}

.place-location {
    font-size: 14px;
    color: #777;
    margin-bottom: 10px;
}

.place-description {
    font-size: 14px;
    line-height: 1.6;
    color: #666;
    margin-bottom: 15px;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.details-btn {
    display: inline-block;
    padding: 8px 20px;
    background-color: #11204A;
    color: #fff;
    border: none;
    border-radius: 4px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.details-btn:hover {
    background-color: #1a3370;
}

/* Responsive styles */
@media (max-width: 992px) {
    .places-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .page-title {
        padding: 80px 0;
    }
    
    .page-title h1 {
        font-size: 36px;
    }
}

@media (max-width: 768px) {
    .places-grid {
        grid-template-columns: 1fr;
    }
    
    .intro-heading {
        font-size: 28px;
    }
    
    .intro-paragraph {
        font-size: 16px;
    }
}

/* Preloader */
.preloader {
    position: fixed;
    left: 0;
    top: 0;
    z-index: 999;
    width: 100%;
    height: 100%;
    background-color: #ffffff;
    background-position: center center;
    background-repeat: no-repeat;
    background-image: url(../images/icons/preloader.svg);
} 