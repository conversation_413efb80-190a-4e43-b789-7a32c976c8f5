.img-style{
	border: 1px solid #d6d6d6;
	height: 200px;
	border-radius: 1rem;
	/* box-shadow: 10px 10px 5px lightblue; */
	margin-right: 30px;
	/* transition: transform 0.5s ease, box-shadow 0.5s ease; */
	/* display: inline-block; */
	background: white;
	padding: 20px;
	border-radius: 10px;
	box-shadow: 5px 5px 15px rgba(0, 0, 0, 0.2), -5px -5px 15px rgba(255, 255, 255, 0.8);
	transition: transform 0.6s ease, box-shadow 0.6s ease;	
	cursor: pointer;
	transform-style: preserve-3d;
}
.img-style:hover{
	transform: scale(0.95);
  	box-shadow: inset 0 4px 6px rgba(0, 0, 0, 0.2);	
}
.partner-block{
	display: flex;
	justify-content: center;
	align-items: center;
	flex-direction: column;
	/* gap: 0.7rem; */
}
.client-block{
	display: flex;
	justify-content: center;
	align-items: center;
}