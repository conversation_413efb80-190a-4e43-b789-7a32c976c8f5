/* Modern Event Agenda Section - Website Theme Colors */
.event-agenda-section {
    padding: 80px 0;
    background: linear-gradient(135deg, #FBD4D2 0%, #ffffff 50%, #f8f9fa 100%);
    position: relative;
    overflow: hidden;
}

.event-agenda-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="2" fill="%23FBD4D2" opacity="0.3"/></svg>') repeat;
    background-size: 50px 50px;
    z-index: 1;
}

.event-agenda-section .auto-container {
    position: relative;
    z-index: 2;
}

.event-agenda-section .sec-title .text {
    font-size: 18px;
    color: #57428D;
    font-weight: 700;
    margin-top: 15px;
    padding: 12px 25px;
    background: linear-gradient(135deg, rgba(87, 66, 141, 0.1) 0%, rgba(251, 212, 210, 0.3) 100%);
    border-radius: 25px;
    display: inline-block;
    border: 2px solid rgba(87, 66, 141, 0.2);
    letter-spacing: 0.5px;
}

.agenda-container {
    max-width: 900px;
    margin: 60px auto 0;
    position: relative;
}

/* Modern Timeline */
.agenda-container::before {
    content: '';
    position: absolute;
    left: 25px;
    top: 0;
    bottom: 0;
    width: 4px;
    background: linear-gradient(to bottom, #4965AD 0%, #57428D 50%, #FBD4D2 100%);
    border-radius: 2px;
    z-index: 1;
}

.agenda-day {
    margin-bottom: 50px;
    padding-left: 70px;
    position: relative;
    background: #ffffff;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(87, 66, 141, 0.1);
    border: 2px solid rgba(251, 212, 210, 0.5);
    overflow: hidden;
    transition: all 0.4s ease;
}

.agenda-day::before {
    content: '';
    position: absolute;
    left: -45px;
    top: 35px;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    z-index: 2;
    border: 4px solid #ffffff;
    box-shadow: 0 0 0 3px rgba(251, 212, 210, 0.5);
}

.conference-day {
    border-left: 6px solid #4965AD;
}

.conference-day::before {
    background: linear-gradient(135deg, #4965AD 0%, #57428D 100%);
}

.workshop-day {
    border-left: 6px solid #57428D;
}

.workshop-day::before {
    background: linear-gradient(135deg, #57428D 0%, #4965AD 100%);
}

.agenda-day:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 20px 40px rgba(87, 66, 141, 0.2);
}

.day-header {
    margin-bottom: 30px;
    padding: 30px 35px 25px;
    background: linear-gradient(135deg, rgba(251, 212, 210, 0.3) 0%, rgba(255, 255, 255, 0.8) 100%);
    border-bottom: 3px solid rgba(251, 212, 210, 0.6);
    position: relative;
    border-radius: 20px 20px 0 0;
}

.day-header::after {
    content: '';
    position: absolute;
    bottom: -3px;
    left: 50%;
    transform: translateX(-50%);
    width: 100px;
    height: 3px;
    background: linear-gradient(90deg, #4965AD, #57428D);
    border-radius: 2px;
}

.day-header h3 {
    font-size: 28px;
    font-weight: 800;
    margin-bottom: 8px;
    position: relative;
    letter-spacing: -0.5px;
    text-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.conference-day .day-header h3 {
    color: #4965AD;
}

.workshop-day .day-header h3 {
    color: #57428D;
}

.workshop-time {
    font-size: 14px;
    color: #ffffff;
    font-weight: 700;
    padding: 10px 20px;
    background: linear-gradient(135deg, #57428D 0%, #4965AD 100%);
    border-radius: 25px;
    display: inline-block;
    border: 2px solid rgba(87, 66, 141, 0.3);
    margin-top: 10px;
    letter-spacing: 0.5px;
    text-transform: uppercase;
    box-shadow: 0 4px 15px rgba(87, 66, 141, 0.3);
}

.day-events {
    padding: 0 35px 30px;
}

.event-item {
    padding: 20px 0;
    border-bottom: 2px dashed rgba(251, 212, 210, 0.6);
    position: relative;
    transition: all 0.3s ease;
}

.event-item::before {
    content: '▶';
    position: absolute;
    left: -15px;
    top: 50%;
    transform: translateY(-50%);
    color: #4965AD;
    font-size: 12px;
    opacity: 0.7;
}

.workshop-item {
    padding: 18px 0;
    border-bottom: 2px dashed rgba(251, 212, 210, 0.6);
    position: relative;
    transition: all 0.3s ease;
}

.workshop-item::before {
    content: '⚡';
    position: absolute;
    left: -15px;
    top: 50%;
    transform: translateY(-50%);
    color: #57428D;
    font-size: 14px;
    opacity: 0.7;
}

.event-item:hover,
.workshop-item:hover {
    background: linear-gradient(135deg, rgba(251, 212, 210, 0.2) 0%, rgba(255, 255, 255, 0.5) 100%);
    padding-left: 20px;
    border-radius: 10px;
    margin: 5px -20px;
}

.event-content {
    font-size: 17px;
    color: #495057;
    line-height: 1.7;
    font-weight: 500;
    padding-left: 10px;
}

.event-item:hover .event-content {
    color: #4965AD;
    font-weight: 600;
}

.workshop-content {
    font-size: 16px;
    color: #495057;
    line-height: 1.7;
    font-weight: 500;
    padding-left: 10px;
}

.workshop-item:hover .workshop-content {
    color: #57428D;
    font-weight: 600;
}

.workshop-title {
    font-size: 22px;
    color: #57428D;
    font-weight: 800;
    margin-bottom: 0;
    text-transform: uppercase;
    letter-spacing: 1px;
    text-shadow: 0 2px 4px rgba(87, 66, 141, 0.2);
    padding-left: 10px;
}

.last-workshop-item {
    padding: 18px 0 0;
    border-bottom: none;
}

/* Special Event Highlighting */
.event-item:nth-child(2) {
    background: linear-gradient(135deg, rgba(251, 212, 210, 0.4) 0%, rgba(87, 66, 141, 0.1) 100%);
    padding: 25px 20px;
    margin: 15px -20px;
    border-radius: 15px;
    border: 2px solid rgba(87, 66, 141, 0.3);
    box-shadow: 0 5px 20px rgba(87, 66, 141, 0.2);
}

.event-item:nth-child(2)::before {
    content: '⭐';
    color: #57428D;
    font-size: 16px;
}

.event-item:nth-child(2) .event-content {
    color: #57428D;
    font-weight: 700;
    font-size: 18px;
}

/* Modern Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.agenda-day {
    animation: fadeInUp 0.6s ease forwards;
}

.agenda-day:nth-child(1) { animation-delay: 0.1s; }
.agenda-day:nth-child(2) { animation-delay: 0.2s; }
.agenda-day:nth-child(3) { animation-delay: 0.3s; }
.agenda-day:nth-child(4) { animation-delay: 0.4s; }

/* Mobile Responsive */
@media (max-width: 768px) {
    .agenda-container::before {
        left: 15px;
    }
    
    .agenda-day {
        padding-left: 50px;
        margin-bottom: 35px;
    }
    
    .agenda-day::before {
        left: -30px;
        top: 25px;
        width: 14px;
        height: 14px;
    }
    
    .day-header {
        padding: 25px 25px 20px;
    }
    
    .day-header h3 {
        font-size: 24px;
    }
    
    .day-events {
        padding: 0 25px 25px;
    }
    
    .event-content, .workshop-content {
        font-size: 16px;
        padding-left: 8px;
    }
    
    .workshop-title {
        font-size: 20px;
        padding-left: 8px;
    }
    
    .event-item:nth-child(2) {
        margin: 10px -15px;
        padding: 20px 15px;
    }
    
    .event-item:hover,
    .workshop-item:hover {
        margin: 5px -15px;
        padding-left: 15px;
    }
} 