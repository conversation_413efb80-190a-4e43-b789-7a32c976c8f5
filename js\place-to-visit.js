/**
 * ISAR 2026 - Abstract Submission Page JavaScript
 */

document.addEventListener('DOMContentLoaded', function() {
    
    // Add decorative elements to sections
    const addDecorativeElements = function() {
        // Add section icons to each major section
        const sectionIcons = [
            { selector: '.important-dates', icon: 'fa-calendar-check' },
            { selector: '.submission-process', icon: 'fa-file-upload' },
            { selector: '.guidelines-section', icon: 'fa-clipboard-list' }
            // Help section icon is now added directly in HTML
        ];
        
        sectionIcons.forEach(section => {
            const sectionElement = document.querySelector(section.selector);
            if (sectionElement) {
                const icon = document.createElement('div');
                icon.className = 'section-icon';
                icon.innerHTML = `<i class="fas ${section.icon}"></i>`;
                sectionElement.prepend(icon);
            }
        });
        
        // Add decorative circles to abstract header
        const abstractHeader = document.querySelector('.abstract-header');
        if (abstractHeader) {
            const circle1 = document.createElement('div');
            circle1.className = 'deco-circle circle-1';
            abstractHeader.appendChild(circle1);
            
            const circle2 = document.createElement('div');
            circle2.className = 'deco-circle circle-2';
            abstractHeader.appendChild(circle2);
        }
        
        // Wrap abstract intro content in div for z-indexing
        const abstractIntro = document.querySelector('.abstract-intro');
        if (abstractIntro) {
            const content = abstractIntro.innerHTML;
            abstractIntro.innerHTML = `<div class="content">${content}</div>`;
        }
    };
    
    addDecorativeElements();
    
    // Add enhanced fade-in animation with staggered reveal
    const animateOnScroll = function() {
        const sections = [
            '.abstract-header', 
            '.abstract-intro', 
            '.important-dates', 
            '.submission-process', 
            '.category-box', 
            '.guidelines-section', 
            '.structure-type', 
            '.abstract-section.help-section', 
            '.closing-message'
        ];
        
        const observer = new IntersectionObserver((entries) => {
            entries.forEach((entry) => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('reveal');
                    
                    // Stagger animations for child elements
                    const childElements = entry.target.querySelectorAll('.category-item, .structure-item, .date-item, .contact-item, .guidelines-block, .guidelines-list li');
                    childElements.forEach((child, index) => {
                        setTimeout(() => {
                            child.classList.add('child-reveal');
                        }, index * 80);
                    });
                    
                    observer.unobserve(entry.target);
                }
            });
        }, {
            threshold: 0.15,
            rootMargin: '0px 0px -50px 0px'
        });
        
        sections.forEach(section => {
            document.querySelectorAll(section).forEach(element => {
                observer.observe(element);
            });
        });
    };
    
    animateOnScroll();
    
    // Add smooth scrolling for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function(e) {
            e.preventDefault();
            
            const targetId = this.getAttribute('href');
            if (targetId !== '#') {
                const targetElement = document.querySelector(targetId);
                if (targetElement) {
                    window.scrollTo({
                        top: targetElement.offsetTop - 80,
                        behavior: 'smooth'
                    });
                }
            }
        });
    });
    
    // Add interactive hover effects
    const addHoverEffects = function() {
        // Enhanced hover for category items
        const items = document.querySelectorAll('.category-item, .structure-item');
        items.forEach(item => {
            item.addEventListener('mouseenter', function() {
                this.style.transform = 'translateX(10px)';
                this.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.05)';
            });
            
            item.addEventListener('mouseleave', function() {
                this.style.transform = '';
                this.style.boxShadow = '';
            });
        });
        
        // Enhanced hover for date items
        const dateItems = document.querySelectorAll('.date-item');
        dateItems.forEach(item => {
            item.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-8px)';
                this.style.boxShadow = '0 8px 20px rgba(50, 50, 93, 0.10)';
            });
            
            item.addEventListener('mouseleave', function() {
                this.style.transform = '';
                this.style.boxShadow = '';
            });
        });
    };
    
    addHoverEffects();
    
    // Add animation styles
    const addAnimationStyles = function() {
        const style = document.createElement('style');
        style.textContent = `
            .reveal {
                opacity: 0;
                transform: translateY(30px);
                animation: reveal 0.8s cubic-bezier(0.5, 0, 0, 1) forwards;
            }
            
            .child-reveal {
                opacity: 0;
                transform: translateY(20px);
                animation: childReveal 0.5s cubic-bezier(0.5, 0, 0, 1) forwards;
            }
            
            @keyframes reveal {
                0% {
                    opacity: 0;
                    transform: translateY(30px);
                }
                100% {
                    opacity: 1;
                    transform: translateY(0);
                }
            }
            
            @keyframes childReveal {
                0% {
                    opacity: 0;
                    transform: translateY(20px);
                }
                100% {
                    opacity: 1;
                    transform: translateY(0);
                }
            }
            
            .pulse {
                animation: pulsate 1s ease-out;
            }
            
            @keyframes pulsate {
                0% {
                    transform: scale(1);
                    box-shadow: 0 8px 20px rgba(50, 50, 93, 0.10);
                }
                50% {
                    transform: scale(1.1);
                    box-shadow: 0 15px 30px rgba(50, 50, 93, 0.2);
                }
                100% {
                    transform: scale(1);
                    box-shadow: 0 8px 20px rgba(50, 50, 93, 0.10);
                }
            }
            
            .deco-circle {
                animation: rotate 60s linear infinite;
            }
            
            @keyframes rotate {
                from { transform: rotate(0deg); }
                to { transform: rotate(360deg); }
            }
        `;
        document.head.appendChild(style);
    };
    
    addAnimationStyles();
});