/* Places to Visit - Clean Modern Design CSS 
   Purple: #57428D
   Blue: #4965AD
   Light Pink: #FBD4D2
*/

/* Common Styles */
body {
    overflow-x: hidden;
}

.auto-container {
    position: relative;
}

/* Introduction Section with Parallax */
.intro-parallax-section {
    position: relative;
    padding: 100px 0 100px;
    background-color: #f8f9fa;
    overflow: hidden;
    border-bottom: 1px solid #eee;
}

/* Mandala background */
.mandala-bg {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    background-image: url('../images/background/ahmedabad.png');
    background-size: 80%;
    background-position: center;
    background-repeat: no-repeat;
    opacity: 0.08;
    z-index: 0;
}

.intro-content {
    position: relative;
    z-index: 2;
    text-align: center;
    max-width: 800px;
    margin: 0 auto;
}

.intro-content h2 {
    font-size: 42px;
    margin-bottom: 30px;
    color: #57428D;
    font-weight: 700;
    position: relative;
    display: inline-block;
    padding-bottom: 15px;
}

.intro-content h2:after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 80px;
    height: 3px;
    background: #4965AD;
    transform: translateX(-50%);
}

.intro-content p {
    font-size: 18px;
    line-height: 1.7;
    color: #444;
    margin-bottom: 0;
}

/* 3D Slider Gallery Section - Refined */
.slider-gallery-section {
    padding: 80px 0;
    background-color: #fff;
    position: relative;
}

.section-title {
    text-align: center;
    margin-bottom: 50px;
    position: relative;
}

.section-title h2 {
    font-size: 36px;
    color: #57428D;
    font-weight: 700;
    position: relative;
    display: inline-block;
    padding-bottom: 15px;
}

.section-title h2:after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 60px;
    height: 3px;
    background: #4965AD;
    transform: translateX(-50%);
}

.slider-container {
    position: relative;
    padding: 0 50px;
}

.attractions-slider {
    height: 420px;
    padding: 30px 0;
}

.swiper-slide {
    transition: transform 0.4s ease;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
    position: relative;
}

.swiper-slide-active {
    transform: scale(1.05);
}

.attraction-card {
    position: relative;
    width: 100%;
    height: 100%;
    border-radius: 12px;
    overflow: hidden;
}

.attraction-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.attraction-details {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(to top, rgba(0, 0, 0, 0.7), transparent);
    padding: 30px 20px 20px;
    color: #fff;
}

.attraction-title {
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 5px;
}

.attraction-location {
    font-size: 16px;
    color: white;
    opacity: 1;
    font-weight: 500;
    display: flex;
    align-items: center;
}

.attraction-location:before {
    content: "\f3c5"; /* Location pin icon from FontAwesome */
    font-family: "Font Awesome 5 Free";
    font-weight: 900;
    margin-right: 8px;
    font-size: 14px;
}

.swiper-button-next, .swiper-button-prev {
    color: #000000 !important;
    width: 60px !important;
    height: 60px !important;
    background: white !important;
    border-radius: 50% !important;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2) !important;
    transition: all 0.3s ease !important;
    opacity: 1 !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    z-index: 100 !important;
    visibility: visible !important;
    text-shadow: 0px 1px 1px rgba(0,0,0,0.2) !important;
}

.swiper-button-next:after, .swiper-button-prev:after {
    font-size: 24px !important;
    font-weight: bold !important;
    color: #000000 !important;
}

/* Explicitly define arrow icons */
.swiper-button-next:after {
    content: '\2192' !important; /* Unicode right arrow → */
}

.swiper-button-prev:after {
    content: '\2190' !important; /* Unicode left arrow ← */
}

.swiper-button-next:hover, .swiper-button-prev:hover {
    background: #57428D !important;
    color: white !important;
    transform: scale(1.1) !important;
}

.swiper-button-next:hover:after, .swiper-button-prev:hover:after {
    color: white !important;
}

.swiper-button-next {
    right: 20px !important;
}

.swiper-button-prev {
    left: 20px !important;
}

/* Ensure buttons are visible on all device sizes */
@media (max-width: 768px) {
    .swiper-button-next, .swiper-button-prev {
        width: 50px !important;
        height: 50px !important;
        display: flex !important;
        opacity: 1 !important;
    }
    
    .swiper-button-next:after, .swiper-button-prev:after {
        font-size: 18px !important;
    }
    
    .swiper-button-next {
        right: 15px !important;
    }
    
    .swiper-button-prev {
        left: 15px !important;
    }
}

/* Location Tabs and Filters Section - Clean design */
.places-filter-section {
    padding: 60px 0 40px;
    background-color: #f8f9fa;
    position: relative;
    border-top: 1px solid #eee;
    border-bottom: 1px solid #eee;
}

.location-tabs {
    display: flex;
    justify-content: center;
    margin-bottom: 30px;
}

.location-tab {
    background: #fff;
    border: none;
    padding: 15px 40px;
    font-size: 18px;
    font-weight: 600;
    color: #333;
    cursor: pointer;
    transition: all 0.3s ease;
    border-radius: 50px;
    margin: 0 10px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
}

.location-tab.active, .location-tab:hover {
    background: #57428D;
    color: #fff;
}

.category-filters {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    margin-bottom: 30px;
}

.category-filter {
    background: transparent;
    border: 1px solid #4965AD;
    padding: 10px 20px;
    font-size: 15px;
    font-weight: 500;
    color: #4965AD;
    cursor: pointer;
    transition: all 0.3s ease;
    border-radius: 30px;
    margin: 5px;
}

.category-filter.active, .category-filter:hover {
    background: #4965AD;
    color: #fff;
}

/* Places Grid Section */
.places-grid-section {
    padding: 70px 0;
    background-color: #fff;
}

.places-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(340px, 1fr));
    gap: 30px;
    margin-top: 30px;
}

.place-card {
    position: relative;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    background: #fff;
    cursor: pointer;
    height: 100%;
    display: flex;
    flex-direction: column;
    border: 1px solid #f0f0f0;
    animation: none;
}

.place-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 15px 35px rgba(87, 66, 141, 0.15);
    border-color: rgba(87, 66, 141, 0.1);
}

.place-image-container {
    position: relative;
    overflow: hidden;
    border-radius: 15px 15px 0 0;
}

.place-image {
    width: 100%;
    height: 220px;
    object-fit: cover;
    transition: all 0.5s ease;
}

.place-card:hover .place-image {
    transform: scale(1.05);
}

.place-image-container:after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to bottom, rgba(0,0,0,0.1) 0%, rgba(0,0,0,0) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.place-card:hover .place-image-container:after {
    opacity: 1;
}

.place-content {
    padding: 25px;
    background: #fff;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
}

.place-title {
    font-size: 22px;
    font-weight: 700;
    color: #333;
    margin-bottom: 12px;
    position: relative;
    padding-bottom: 12px;
    transition: color 0.3s ease;
}

.place-title:after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 40px;
    height: 3px;
    background: #57428D;
    border-radius: 2px;
    transition: width 0.3s ease;
}

.place-card:hover .place-title {
    color: #57428D;
}

.place-card:hover .place-title:after {
    width: 60px;
}

.place-description {
    font-size: 15px;
    color: #666;
    margin-bottom: 20px;
    line-height: 1.6;
    flex-grow: 1;
}

.place-category-wrapper {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 15px;
}

.place-category {
    display: inline-block;
    padding: 6px 12px;
    color: #fff;
    font-size: 12px;
    border-radius: 20px;
    margin-right: 5px;
    margin-bottom: 5px;
    font-weight: 500;
    letter-spacing: 0.5px;
    position: relative;
    overflow: hidden;
    z-index: 1;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.place-category:after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255,255,255,0.15);
    transform: translateX(-100%);
    transition: all 0.3s ease;
    z-index: -1;
}

.place-card:hover .place-category:after {
    transform: translateX(0);
}

.place-category.religious {
    background: #7E57C2;
}

.place-category.wildlife {
    background: #43A047;
}

.place-category.heritage {
    background: #FB8C00;
}

.place-category.natural {
    background: #00ACC1;
}

.place-category.modern {
    background: #E53935;
}

.place-category.museums {
    background: #FBD4D2;
    color: #57428D;
}

.place-category.architecture {
    background: #5D4037;
}

.place-category.historical {
    background: #57428D;
}

.place-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: auto;
}

.place-location {
    font-size: 14px;
    color: #666;
    display: flex;
    align-items: center;
}

.place-location:before {
    content: "\f3c5"; /* Location pin icon from FontAwesome */
    font-family: "Font Awesome 5 Free";
    font-weight: 900;
    margin-right: 8px;
    font-size: 14px;
    color: #57428D;
}

.view-details {
    display: inline-block;
    padding: 10px 20px;
    background: #57428D;
    color: #fff;
    border-radius: 30px;
    font-size: 15px;
    font-weight: 500;
    transition: all 0.3s ease;
    text-decoration: none;
    border: 1px solid transparent;
    box-shadow: 0 4px 8px rgba(87, 66, 141, 0.2);
    position: relative;
    overflow: hidden;
    cursor: pointer;
}

.view-details:after {
    content: "\f061"; /* Arrow right icon from FontAwesome */
    font-family: "Font Awesome 5 Free";
    font-weight: 900;
    margin-left: 8px;
    font-size: 14px;
    transition: all 0.3s ease;
}

.view-details:hover {
    background: #fff;
    color: #57428D;
    border-color: #57428D;
    box-shadow: 0 6px 12px rgba(87, 66, 141, 0.3);
    transform: translateY(-2px);
}

.view-details:hover:after {
    transform: translateX(3px);
}

/* Simple Modal Styles */
.place-detail-modal {
    display: none;
    position: fixed;
    z-index: 9999;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(3px);
}

.place-detail-modal.show {
    display: block;
}

.modal-content {
    position: relative;
    background-color: #fff;
    margin: 50px auto;
    width: 90%;
    max-width: 800px;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    overflow: hidden;
}

.simple-modal {
    padding: 0;
    position: relative;
}

.close-modal {
    position: absolute;
    top: 12px;
    right: 12px;
    width: 35px;
    height: 35px;
    background: rgba(255, 255, 255, 0.9);
    color: #333;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    cursor: pointer;
    z-index: 10;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

.close-modal:hover {
    background: #fff;
    transform: scale(1.1);
}

.simple-modal-header {
    padding: 20px 25px;
    background: #57428D;
    color: white;
}

.simple-modal-header h2 {
    font-size: 24px;
    margin-bottom: 5px;
    color: white;
}

.simple-location {
    display: flex;
    align-items: center;
    color: rgba(255, 255, 255, 0.9);
    font-size: 16px;
}

.simple-location i {
    margin-right: 8px;
}

.simple-modal-image {
    width: 100%;
    height: 300px;
    overflow: hidden;
}

.simple-modal-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.simple-modal-content {
    padding: 25px;
}

.simple-modal-content h3 {
    margin-top: 15px;
    margin-bottom: 15px;
    font-size: 20px;
    font-weight: 600;
    color: #57428D;
}

.simple-modal-content h3:first-child {
    margin-top: 0;
}

.simple-modal-content p {
    margin-bottom: 20px;
    line-height: 1.6;
    color: #444;
}

.simple-highlights {
    margin-bottom: 25px;
    padding-left: 25px;
}

.simple-highlights li {
    margin-bottom: 8px;
    line-height: 1.5;
    position: relative;
}

.simple-highlights li:before {
    content: "✓";
    position: absolute;
    left: -25px;
    color: #57428D;
    font-weight: bold;
}

.simple-modal-info {
    background: #f5f5f5;
    padding: 20px;
    border-radius: 8px;
    margin-top: 15px;
}

.simple-modal-info .info-item {
    margin-bottom: 10px;
}

.simple-modal-info .info-item:last-child {
    margin-bottom: 0;
}

.simple-modal-info strong {
    color: #57428D;
    margin-right: 5px;
}

@media only screen and (max-width: 767px) {
    .modal-content {
        margin: 30px auto;
        width: 95%;
    }
    
    .simple-modal-image {
        height: 200px;
    }
    
    .simple-modal-header h2 {
        font-size: 20px;
    }
    
    .simple-modal-content {
        padding: 15px;
    }
}

@media only screen and (max-width: 480px) {
    .simple-modal-image {
        height: 180px;
    }
    
    .simple-modal-header {
        padding: 15px 20px;
    }
    
    .simple-modal-header h2 {
        font-size: 18px;
    }
    
    .simple-location {
        font-size: 14px;
    }
    
    .simple-modal-content h3 {
        font-size: 18px;
    }
}

/* Now adding Magazine Style Layout */
.magazine-section {
    padding: 70px 0;
    background-color: #fff;
}

.magazine-title-section {
    text-align: center;
    margin-bottom: 50px;
}

.magazine-title-section h2 {
    font-size: 36px;
    color: #57428D;
    font-weight: 700;
    position: relative;
    display: inline-block;
    padding-bottom: 15px;
}

.magazine-title-section h2:after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 60px;
    height: 3px;
    background: #4965AD;
    transform: translateX(-50%);
}

.places-magazine-grid {
    display: grid;
    grid-template-columns: repeat(12, 1fr);
    grid-auto-rows: minmax(180px, auto);
    gap: 20px;
    margin: 0 auto;
    width: 100%;
}

.magazine-item {
    position: relative;
    overflow: hidden;
    border-radius: 12px;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
}

.magazine-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 25px rgba(0, 0, 0, 0.12);
}

.magazine-feature {
    grid-column: span 8;
    grid-row: span 2;
}

.magazine-medium {
    grid-column: span 6;
    grid-row: span 2;
}

.magazine-small {
    grid-column: span 4;
    grid-row: span 1;
}

.magazine-img-container {
    width: 100%;
    height: 100%;
    min-height: 250px;
}

.magazine-img-container img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.magazine-item:hover .magazine-img-container img {
    transform: scale(1.05);
}

.magazine-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    background: linear-gradient(to top, rgba(0, 0, 0, 0.7), transparent);
    padding: 30px 25px 20px;
    color: #fff;
}

.magazine-category {
    display: inline-block;
    padding: 5px 12px;
    background: #4965AD;
    color: #fff;
    font-size: 12px;
    border-radius: 20px;
    margin-bottom: 10px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.magazine-title {
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 5px;
    line-height: 1.3;
}

.magazine-medium .magazine-title {
    font-size: 20px;
}

.magazine-small .magazine-title {
    font-size: 18px;
}

.magazine-location {
    font-size: 14px;
    opacity: 0.9;
    display: flex;
    align-items: center;
}

.magazine-location:before {
    content: "\f3c5"; /* Location pin icon from FontAwesome */
    font-family: "Font Awesome 5 Free";
    font-weight: 900;
    margin-right: 8px;
    font-size: 12px;
}

.magazine-description {
    font-size: 14px;
    line-height: 1.6;
    margin-top: 10px;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    opacity: 0.9;
}

.magazine-feature .magazine-description {
    display: block;
    -webkit-line-clamp: 3;
}

/* Category badges */
.magazine-category.historical { background-color: #57428D; }
.magazine-category.religious { background-color: #7E57C2; }
.magazine-category.wildlife { background-color: #43A047; }
.magazine-category.heritage { background-color: #FB8C00; }
.magazine-category.natural { background-color: #00ACC1; }
.magazine-category.modern { background-color: #E53935; }
.magazine-category.museums { background-color: #FBD4D2; color: #57428D; }
.magazine-category.architecture { background-color: #5D4037; }

/* Responsive adjustments for magazine grid */
@media only screen and (max-width: 1200px) {
    .magazine-feature {
        grid-column: span 12;
    }
    
    .magazine-medium {
        grid-column: span 6;
    }
    
    .magazine-small {
        grid-column: span 6;
    }
}

@media only screen and (max-width: 767px) {
    .places-magazine-grid {
        grid-template-columns: repeat(6, 1fr);
    }
    
    .magazine-feature {
        grid-column: span 6;
    }
    
    .magazine-medium {
        grid-column: span 6;
    }
    
    .magazine-small {
        grid-column: span 3;
        grid-row: auto;
    }
}

@media only screen and (max-width: 575px) {
    .places-magazine-grid {
        grid-template-columns: 1fr;
        grid-auto-rows: auto;
    }
    
    .magazine-feature, 
    .magazine-medium, 
    .magazine-small {
        grid-column: span 1;
        grid-row: auto;
    }
    
    .magazine-img-container {
        height: 250px;
    }
}

/* Remove all custom heading styles */
.section-heading-container,
.section-main-heading,
.section-main-heading:after,
.section-category-container,
.section-category-heading,
.main-section-heading,
.category-heading,
.category-heading h3,
.category-heading h3:after,
.category-cards,
.main-section-heading:after {
    display: none;
}

/* Place Cards - remove any custom styling we added */
.place-card {
    animation: none;
} 